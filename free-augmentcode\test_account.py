#!/usr/bin/env python3
"""
测试账号检测功能
"""

from utils.account_detector import get_all_account_info, format_account_summary, get_augment_account_info
from utils.paths import get_storage_path, get_db_path
from utils.account_manager import get_account_manager, get_current_login_info
from utils.cookie_login import create_cookie_login_manager

def main():
    print("🔍 测试新版账号管理功能")
    print("=" * 50)

    # 检查当前登录状态
    print("🔍 检查当前登录状态...")
    try:
        login_info = get_current_login_info()
        if login_info:
            print("✅ 发现已配置的登录信息:")
            print(f"  📧 邮箱: {login_info['email']}")
            print(f"  👤 用户名: {login_info['username']}")
            print(f"  🕒 最后使用: {login_info['last_used']}")
            print(f"  🍪 Cookie长度: {len(login_info.get('cookie_string', ''))} 字符")
        else:
            print("❌ 未找到配置的登录信息")
            print("💡 请使用GUI界面配置账号和Cookie信息")

    except Exception as e:
        print(f"❌ 检查登录状态失败: {e}")

    print("\n" + "=" * 50)

    # 测试账号管理器
    print("🔍 测试账号管理器...")
    try:
        account_manager = get_account_manager()
        summary = account_manager.get_account_summary()

        print(f"📊 账号汇总:")
        print(f"  - 总账号数: {summary['total_accounts']}")
        print(f"  - 有活跃账号: {summary['has_active_account']}")
        print(f"  - 活跃邮箱: {summary['active_email']}")
        print(f"  - 配置文件存在: {summary['config_file_exists']}")

        # 列出所有账号
        accounts = account_manager.list_accounts()
        if accounts:
            print(f"\n📋 已配置的账号 ({len(accounts)} 个):")
            for i, account in enumerate(accounts, 1):
                print(f"  {i}. {account['email']} - {account.get('cookie_preview', 'No Cookie')}")
        else:
            print("\n📋 暂无已配置的账号")

    except Exception as e:
        print(f"❌ 账号管理器测试失败: {e}")

    print("\n" + "=" * 50)

    # 测试AugmentCode账号检测（新版）
    print("🔍 检查 AugmentCode 账号信息（新版）...")
    try:
        augment_info = get_augment_account_info()
        print(f"登录状态: {augment_info['logged_in']}")
        print(f"登录方式: {augment_info.get('login_method', 'unknown')}")
        print(f"邮箱: {augment_info['email']}")
        print(f"用户名: {augment_info['username']}")
        print(f"用户ID: {augment_info['user_id']}")
        print(f"登录时间: {augment_info['login_time']}")

        # Cookie信息
        cookie_info = augment_info.get('cookie_info', {})
        if cookie_info.get('has_cookie'):
            print(f"Cookie信息: {cookie_info['cookie_length']} 字符")

        print(f"订阅信息: {len(augment_info['subscription_info'])} 项")

        if augment_info.get('error'):
            print(f"❌ 错误: {augment_info['error']}")

    except Exception as e:
        print(f"❌ AugmentCode账号检测失败: {e}")

    print("\n" + "=" * 50)
    
    # 测试完整账号信息
    print("🔍 获取完整账号信息...")
    try:
        account_info = get_all_account_info()
        formatted_info = format_account_summary(account_info)
        print(formatted_info)
        
    except Exception as e:
        print(f"❌ 完整账号信息获取失败: {e}")
    
    print("\n" + "=" * 50)
    
    # 测试Cookie登录管理器
    print("🔍 测试Cookie登录管理器...")
    try:
        cookie_manager = create_cookie_login_manager()

        # 测试Cookie格式验证
        test_cookies = [
            "session=abc123; auth=def456",
            "invalid_cookie_format",
            "key1=value1; key2=value2; key3=value3"
        ]

        print("🍪 Cookie格式验证测试:")
        for i, cookie in enumerate(test_cookies, 1):
            is_valid = cookie_manager.validate_cookie_format(cookie)
            status = "✅" if is_valid else "❌"
            print(f"  {i}. {status} {cookie[:30]}{'...' if len(cookie) > 30 else ''}")

        # 测试Cookie解析
        valid_cookie = "session=abc123; auth=def456; user=test"
        parsed = cookie_manager.parse_cookie_string(valid_cookie)
        print(f"\n🔧 Cookie解析测试:")
        print(f"  原始: {valid_cookie}")
        print(f"  解析: {parsed}")

    except Exception as e:
        print(f"❌ Cookie登录管理器测试失败: {e}")

    print("\n" + "=" * 50)

    # 检查旧版存储文件（兼容性）
    print("🔍 检查旧版存储文件（兼容性）...")
    try:
        import json
        import os

        storage_path = get_storage_path()
        if os.path.exists(storage_path):
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)

            print(f"📊 Storage 文件包含 {len(storage_data)} 个键")

            # 查找可能包含账号信息的键
            account_keys = []
            for key in storage_data.keys():
                if any(term in key.lower() for term in ['user', 'account', 'email', 'login', 'auth', 'augment']):
                    account_keys.append(key)

            print(f"🔍 找到 {len(account_keys)} 个可能相关的键:")
            for key in account_keys[:5]:  # 只显示前5个
                value = storage_data[key]
                if isinstance(value, str) and len(value) > 50:
                    print(f"  - {key}: {value[:30]}...")
                else:
                    print(f"  - {key}: {str(value)[:50]}")

        else:
            print("❌ storage.json 文件不存在")

    except Exception as e:
        print(f"❌ 分析 storage.json 失败: {e}")

if __name__ == "__main__":
    main()
