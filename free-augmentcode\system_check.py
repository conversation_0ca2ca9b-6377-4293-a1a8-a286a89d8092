#!/usr/bin/env python3
"""
系统完整性检查
"""

import sys
import os
import importlib.util

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    exists = os.path.exists(file_path)
    status = "✅" if exists else "❌"
    print(f"{status} {file_path} {description}")
    return exists

def check_module_import(module_name, description=""):
    """检查模块是否可以导入"""
    try:
        if '.' in module_name:
            # 处理相对导入
            parts = module_name.split('.')
            module = __import__(module_name, fromlist=[parts[-1]])
        else:
            module = __import__(module_name)
        print(f"✅ {module_name} {description}")
        return True, module
    except ImportError as e:
        print(f"❌ {module_name} {description} - 导入失败: {e}")
        return False, None
    except Exception as e:
        print(f"⚠️ {module_name} {description} - 异常: {e}")
        return False, None

def check_function_exists(module, function_name, description=""):
    """检查函数是否存在"""
    if module and hasattr(module, function_name):
        print(f"  ✅ {function_name}() {description}")
        return True
    else:
        print(f"  ❌ {function_name}() {description}")
        return False

def main():
    """主检查函数"""
    print("🔍 Free AugmentCode 系统完整性检查")
    print("=" * 60)
    
    # 1. 检查核心文件
    print("\n📁 核心文件检查")
    print("-" * 30)
    
    core_files = [
        ("gui.py", "主GUI文件"),
        ("enhanced_account_methods.py", "增强账号方法"),
        ("utils/account_manager.py", "账号管理器"),
        ("utils/subscription_checker.py", "订阅检查器"),
        ("utils/cookie_login.py", "Cookie登录"),
        ("utils/account_detector.py", "账号检测器"),
        ("gui/account_status_page.py", "账号状态页面"),
        ("gui/account_status_methods.py", "账号状态方法"),
    ]
    
    file_check_results = []
    for file_path, description in core_files:
        result = check_file_exists(file_path, description)
        file_check_results.append((file_path, result))
    
    # 2. 检查模块导入
    print("\n📦 模块导入检查")
    print("-" * 30)
    
    modules_to_check = [
        ("utils.account_manager", "账号管理模块"),
        ("utils.subscription_checker", "订阅检查模块"),
        ("utils.cookie_login", "Cookie登录模块"),
        ("utils.account_detector", "账号检测模块"),
        ("enhanced_account_methods", "增强方法模块"),
    ]
    
    module_results = []
    for module_name, description in modules_to_check:
        success, module = check_module_import(module_name, description)
        module_results.append((module_name, success, module))
    
    # 3. 检查关键函数
    print("\n🔧 关键函数检查")
    print("-" * 30)
    
    function_checks = [
        ("utils.account_manager", "get_current_login_info", "获取当前登录信息"),
        ("utils.account_manager", "get_account_subscription_info", "获取订阅信息"),
        ("utils.subscription_checker", "quick_check_subscription", "快速检查订阅"),
        ("utils.cookie_login", "quick_validate_login", "快速验证登录"),
        ("utils.account_detector", "get_all_account_info", "获取所有账号信息"),
        ("enhanced_account_methods", "get_enhanced_account_info", "增强账号信息获取"),
    ]
    
    function_results = []
    for module_name, function_name, description in function_checks:
        # 找到对应的模块
        module = None
        for mod_name, success, mod_obj in module_results:
            if mod_name == module_name and success:
                module = mod_obj
                break
        
        if module:
            result = check_function_exists(module, function_name, description)
            function_results.append((module_name, function_name, result))
        else:
            print(f"  ❌ {function_name}() {description} - 模块未加载")
            function_results.append((module_name, function_name, False))
    
    # 4. 检查依赖库
    print("\n📚 依赖库检查")
    print("-" * 30)
    
    dependencies = [
        ("tkinter", "GUI框架"),
        ("requests", "HTTP请求库"),
        ("json", "JSON处理"),
        ("sqlite3", "数据库"),
        ("datetime", "日期时间"),
        ("threading", "多线程"),
        ("os", "操作系统接口"),
        ("sys", "系统接口"),
    ]
    
    optional_dependencies = [
        ("selenium", "浏览器自动化"),
        ("bs4", "BeautifulSoup HTML解析"),
        ("lxml", "XML解析器"),
    ]
    
    dep_results = []
    for dep_name, description in dependencies:
        success, _ = check_module_import(dep_name, f"(必需) {description}")
        dep_results.append((dep_name, success, True))  # True表示必需
    
    for dep_name, description in optional_dependencies:
        success, _ = check_module_import(dep_name, f"(可选) {description}")
        dep_results.append((dep_name, success, False))  # False表示可选
    
    # 5. 检查数据文件
    print("\n💾 数据文件检查")
    print("-" * 30)
    
    data_files = [
        ("accounts.db", "账号数据库"),
        ("config.json", "配置文件"),
    ]
    
    data_results = []
    for file_name, description in data_files:
        exists = os.path.exists(file_name)
        status = "✅" if exists else "⚠️"
        print(f"{status} {file_name} {description} {'(存在)' if exists else '(将在首次运行时创建)'}")
        data_results.append((file_name, exists))
    
    # 6. 功能测试
    print("\n🧪 功能测试")
    print("-" * 30)
    
    try:
        # 测试账号管理器
        from utils.account_manager import get_current_login_info
        current_info = get_current_login_info()
        if current_info:
            print(f"✅ 账号管理器 - 检测到登录账号: {current_info['email']}")
        else:
            print("⚠️ 账号管理器 - 未检测到登录账号")
    except Exception as e:
        print(f"❌ 账号管理器测试失败: {e}")
    
    try:
        # 测试账号检测器
        from utils.account_detector import get_all_account_info
        account_info = get_all_account_info()
        augment_account = account_info.get('augment_account', {})
        if augment_account.get('logged_in'):
            print(f"✅ 账号检测器 - 检测到AugmentCode登录")
        else:
            print("⚠️ 账号检测器 - 未检测到AugmentCode登录")
    except Exception as e:
        print(f"❌ 账号检测器测试失败: {e}")
    
    # 7. 生成总结报告
    print("\n📊 检查结果总结")
    print("=" * 60)
    
    # 文件检查总结
    file_success = sum(1 for _, result in file_check_results if result)
    file_total = len(file_check_results)
    print(f"📁 核心文件: {file_success}/{file_total} 通过")
    
    # 模块检查总结
    module_success = sum(1 for _, success, _ in module_results if success)
    module_total = len(module_results)
    print(f"📦 模块导入: {module_success}/{module_total} 通过")
    
    # 函数检查总结
    function_success = sum(1 for _, _, result in function_results if result)
    function_total = len(function_results)
    print(f"🔧 关键函数: {function_success}/{function_total} 通过")
    
    # 依赖检查总结
    required_deps = [result for dep, result, required in dep_results if required]
    optional_deps = [result for dep, result, required in dep_results if not required]
    
    required_success = sum(required_deps)
    required_total = len(required_deps)
    optional_success = sum(optional_deps)
    optional_total = len(optional_deps)
    
    print(f"📚 必需依赖: {required_success}/{required_total} 通过")
    print(f"📚 可选依赖: {optional_success}/{optional_total} 通过")
    
    # 整体评估
    print(f"\n🎯 整体评估")
    print("-" * 30)
    
    total_checks = file_total + module_total + function_total + required_total
    total_success = file_success + module_success + function_success + required_success
    
    success_rate = (total_success / total_checks) * 100 if total_checks > 0 else 0
    
    if success_rate >= 90:
        status = "🟢 优秀"
    elif success_rate >= 75:
        status = "🟡 良好"
    elif success_rate >= 60:
        status = "🟠 一般"
    else:
        status = "🔴 需要修复"
    
    print(f"成功率: {success_rate:.1f}% ({total_success}/{total_checks})")
    print(f"系统状态: {status}")
    
    # 建议
    print(f"\n💡 建议")
    print("-" * 30)
    
    if success_rate < 100:
        print("• 检查失败的模块和函数")
        if optional_success < optional_total:
            print("• 安装可选依赖以获得完整功能: python install_enhanced_dependencies.py")
        if file_success < file_total:
            print("• 确保所有核心文件都存在")
        if module_success < module_total:
            print("• 检查Python路径和模块导入")
    else:
        print("• 系统状态良好，所有功能正常")
    
    print("• 运行 python gui.py 启动主程序")
    print("• 运行 python test_enhanced_methods.py 测试增强功能")

if __name__ == "__main__":
    main()
