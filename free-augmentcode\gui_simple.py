import customtkinter as ctk
from tkinter import messagebox
import threading
import time
import os
from datetime import datetime

# 导入现有的功能模块
from utils.paths import (
    get_home_dir, get_app_data_dir, get_storage_path, 
    get_db_path, get_machine_id_path, get_workspace_storage_path
)
from utils.extension_checker import get_augment_extension_info
from utils.account_detector import get_all_account_info
from utils.account_manager import get_current_login_info, format_account_summary
from augutils.json_modifier import modify_telemetry_ids
from augutils.sqlite_modifier import clean_augment_data
from augutils.workspace_cleaner import clean_workspace_storage

# 设置主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class SimpleGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Free AugmentCode - 简化版")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化变量
        self.is_running = False
        self.results = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ctk.CTkLabel(
            self.root, 
            text="🚀 Free AugmentCode", 
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 扩展状态检查
        self.check_extension_status()
        
        # 警告提示
        warning_frame = ctk.CTkFrame(self.root)
        warning_frame.pack(fill="x", padx=20, pady=10)
        
        warning_label = ctk.CTkLabel(
            warning_frame,
            text="⚠️ 使用前请确保：\n1. 完全退出 VS Code\n2. 退出 AugmentCode 插件",
            font=ctk.CTkFont(size=14),
            text_color="orange"
        )
        warning_label.pack(pady=15)
        
        # 主要操作按钮
        self.start_button = ctk.CTkButton(
            self.root,
            text="🚀 开始清理 AugmentCode 数据",
            font=ctk.CTkFont(size=18, weight="bold"),
            height=60,
            command=self.start_cleaning
        )
        self.start_button.pack(fill="x", padx=20, pady=20)
        
        # 进度显示
        self.progress_label = ctk.CTkLabel(
            self.root, 
            text="准备就绪", 
            font=ctk.CTkFont(size=14)
        )
        self.progress_label.pack(pady=(10, 5))
        
        self.progress_bar = ctk.CTkProgressBar(self.root)
        self.progress_bar.pack(fill="x", padx=20, pady=(0, 20))
        self.progress_bar.set(0)
        
        # 日志显示
        log_label = ctk.CTkLabel(
            self.root, 
            text="📝 操作日志", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        log_label.pack(pady=(10, 5))
        
        self.log_text = ctk.CTkTextbox(self.root, height=200)
        self.log_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # 底部按钮
        button_frame = ctk.CTkFrame(self.root)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        clear_button = ctk.CTkButton(
            button_frame,
            text="清空日志",
            command=self.clear_log,
            width=100
        )
        clear_button.pack(side="left", padx=10, pady=10)
        
        refresh_button = ctk.CTkButton(
            button_frame,
            text="刷新状态",
            command=self.check_extension_status,
            width=100
        )
        refresh_button.pack(side="left", padx=10, pady=10)
        
        exit_button = ctk.CTkButton(
            button_frame,
            text="退出",
            command=self.root.quit,
            width=100
        )
        exit_button.pack(side="right", padx=10, pady=10)
        
    def check_extension_status(self):
        """检查扩展和账号状态"""
        try:
            # 检查扩展状态
            ext_info = get_augment_extension_info()
            if ext_info['installed']:
                status = "启用" if ext_info['enabled'] else "禁用"
                self.log_message(f"🔌 AugmentCode 扩展: {ext_info['display_name']} v{ext_info['version']} ({status})", "INFO")
            else:
                self.log_message("🔌 未检测到 AugmentCode 扩展", "INFO")

            # 检查账号状态
            login_info = get_current_login_info()

            if login_info:
                email = login_info.get('email', '')
                login_method = login_info.get('login_method', 'cookie')
                self.log_message(f"👤 AugmentCode 账号: 已登录 ({email}) - {login_method.upper()}", "INFO")
                self.log_message(f"🕒 登录时间: {login_info.get('last_used', '未知')}", "INFO")
            else:
                # 检查是否有旧的活动记录
                account_info = get_all_account_info()
                augment_activity = account_info['augment_activity']

                if augment_activity.get('is_active'):
                    activity_count = len(augment_activity.get('recent_activity', []))
                    self.log_message(f"👤 AugmentCode 账号: 检测到旧活动记录 ({activity_count} 项)", "INFO")
                else:
                    self.log_message("👤 AugmentCode 账号: 未配置登录信息", "INFO")
                    self.log_message("💡 请使用完整版GUI配置账号和Cookie信息", "INFO")

        except Exception as e:
            self.log_message(f"🔌 状态检查失败: {str(e)}", "WARNING")
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")
        
    def update_progress(self, value, message=""):
        """更新进度条"""
        self.progress_bar.set(value)
        if message:
            self.progress_label.configure(text=message)
        self.root.update_idletasks()
        
    def start_cleaning(self):
        """开始清理操作"""
        if self.is_running:
            messagebox.showwarning("警告", "操作正在进行中，请等待完成！")
            return
            
        # 确认对话框
        result = messagebox.askyesno(
            "确认操作", 
            "此操作将清理 AugmentCode 相关数据。\n\n"
            "所有原始数据将被备份。\n\n"
            "确定要继续吗？"
        )
        
        if not result:
            return
            
        # 在新线程中执行清理操作
        self.is_running = True
        self.start_button.configure(state="disabled", text="正在清理...")
        
        thread = threading.Thread(target=self.perform_cleaning)
        thread.daemon = True
        thread.start()
        
    def perform_cleaning(self):
        """执行清理操作"""
        try:
            self.log_message("🚀 开始 AugmentCode 数据清理操作", "INFO")
            self.update_progress(0, "准备开始...")
            
            # 步骤1：修改 Telemetry IDs
            self.log_message("步骤 1/3: 修改 Telemetry IDs", "INFO")
            self.update_progress(0.2, "修改 Telemetry IDs...")
            
            result = modify_telemetry_ids()
            self.log_message("✅ Telemetry IDs 修改完成", "SUCCESS")
            self.update_progress(0.4, "Telemetry IDs 修改完成")
            
            time.sleep(0.5)
            
            # 步骤2：清理数据库
            self.log_message("步骤 2/3: 清理 SQLite 数据库", "INFO")
            self.update_progress(0.5, "清理数据库...")
            
            db_result = clean_augment_data()
            self.log_message(f"✅ 数据库清理完成，删除了 {db_result['deleted_rows']} 条记录", "SUCCESS")
            self.update_progress(0.7, "数据库清理完成")
            
            time.sleep(0.5)
            
            # 步骤3：清理工作区
            self.log_message("步骤 3/3: 清理工作区存储", "INFO")
            self.update_progress(0.8, "清理工作区...")
            
            ws_result = clean_workspace_storage()
            self.log_message(f"✅ 工作区清理完成，删除了 {ws_result['deleted_files_count']} 个文件", "SUCCESS")
            self.update_progress(1.0, "所有操作完成")
            
            # 完成
            self.log_message("🎉 所有清理操作已完成！", "SUCCESS")
            self.log_message("现在可以重新启动 VS Code 并使用新邮箱登录 AugmentCode", "INFO")
            
            # 显示完成对话框
            self.root.after(0, lambda: messagebox.showinfo(
                "操作完成", 
                "AugmentCode 数据清理完成！\n\n"
                "现在可以重新启动 VS Code 并使用新邮箱登录。"
            ))
            
        except Exception as e:
            self.log_message(f"❌ 操作失败: {str(e)}", "ERROR")
            self.root.after(0, lambda: messagebox.showerror("错误", f"操作失败：{str(e)}"))
            
        finally:
            # 重置UI状态
            self.is_running = False
            self.root.after(0, lambda: self.start_button.configure(state="normal", text="🚀 开始清理 AugmentCode 数据"))
            
    def run(self):
        """运行GUI应用"""
        self.log_message("Free AugmentCode 简化版 GUI 已启动", "INFO")
        self.log_message("请在使用前确保完全退出 VS Code 和 AugmentCode 插件", "WARNING")
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = SimpleGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"GUI 启动失败：{str(e)}")

if __name__ == "__main__":
    main()
