#!/usr/bin/env python3
"""
查找AugmentCode的用户认证信息
"""

import sqlite3
import json
import os
import platform
import re

def get_vscode_path():
    """获取VS Code配置路径"""
    if platform.system() == "Windows":
        return os.path.expandvars("%APPDATA%/Code")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Library/Application Support/Code")
    else:  # Linux
        return os.path.expanduser("~/.config/Code")

def find_user_info():
    """查找用户认证信息"""
    vscode_path = get_vscode_path()
    print("🔍 查找AugmentCode用户认证信息")
    print("=" * 60)
    
    # 1. 检查webview相关数据
    print("\n1️⃣ 检查webview相关数据:")
    db_path = os.path.join(vscode_path, "User", "globalStorage", "state.vscdb")
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查找webview相关的augment记录
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%webview%' AND value LIKE '%augment%'")
            rows = cursor.fetchall()
            
            print(f"   找到 {len(rows)} 条webview相关记录:")
            for i, (key, value) in enumerate(rows):
                print(f"   {i+1}. Key: {key}")
                print(f"      Value: {value}")
                print()
            
            conn.close()
        except Exception as e:
            print(f"   ❌ 查询失败: {e}")
    
    # 2. 检查包含邮箱的记录
    print("\n2️⃣ 检查包含邮箱的记录:")
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查找包含邮箱格式的记录
            cursor.execute("SELECT key, value FROM ItemTable WHERE value LIKE '%@%'")
            rows = cursor.fetchall()
            
            email_records = []
            for key, value in rows:
                # 使用正则表达式查找邮箱
                emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', str(value))
                if emails and ('augment' in key.lower() or 'augment' in value.lower()):
                    email_records.append((key, value, emails))
            
            print(f"   找到 {len(email_records)} 条包含邮箱的Augment相关记录:")
            for i, (key, value, emails) in enumerate(email_records):
                print(f"   {i+1}. Key: {key}")
                print(f"      邮箱: {emails}")
                print(f"      Value: {value[:200]}...")
                print()
            
            conn.close()
        except Exception as e:
            print(f"   ❌ 查询失败: {e}")
    
    # 3. 检查GitHub/Microsoft认证信息
    print("\n3️⃣ 检查GitHub/Microsoft认证信息:")
    storage_path = os.path.join(vscode_path, "User", "globalStorage", "storage.json")
    if os.path.exists(storage_path):
        try:
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)
            
            # 查找认证相关信息
            auth_info = {}
            for key, value in storage_data.items():
                if any(term in key.lower() for term in ['github', 'microsoft', 'auth', 'session', 'token']):
                    if isinstance(value, dict):
                        # 查找用户信息
                        for k, v in value.items():
                            if k.lower() in ['email', 'login', 'username', 'name', 'displayname']:
                                if key not in auth_info:
                                    auth_info[key] = {}
                                auth_info[key][k] = v
                    elif isinstance(value, str) and '@' in value:
                        auth_info[key] = value
            
            if auth_info:
                print(f"   找到 {len(auth_info)} 个认证相关信息:")
                for key, value in auth_info.items():
                    print(f"   🔑 {key}:")
                    if isinstance(value, dict):
                        for k, v in value.items():
                            print(f"      {k}: {v}")
                    else:
                        print(f"      {value}")
                    print()
            else:
                print("   ❌ 未找到认证信息")
                
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
    
    # 4. 检查secrets存储
    print("\n4️⃣ 检查可能的secrets存储:")
    # Windows的secrets通常存储在Credential Manager中，但VS Code可能有自己的存储
    secrets_paths = [
        os.path.join(vscode_path, "User", "secrets"),
        os.path.join(vscode_path, "secrets"),
        os.path.join(vscode_path, "User", "globalStorage", "secrets"),
    ]
    
    for secrets_path in secrets_paths:
        if os.path.exists(secrets_path):
            print(f"   ✅ 找到secrets目录: {secrets_path}")
            try:
                for root, dirs, files in os.walk(secrets_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        print(f"      📁 {file_path}")
            except Exception as e:
                print(f"      ❌ 读取失败: {e}")
        else:
            print(f"   ❌ 未找到: {secrets_path}")
    
    # 5. 检查工作区存储
    print("\n5️⃣ 检查工作区存储:")
    workspace_storage = os.path.join(vscode_path, "User", "workspaceStorage")
    if os.path.exists(workspace_storage):
        augment_workspaces = []
        for workspace_dir in os.listdir(workspace_storage):
            workspace_path = os.path.join(workspace_storage, workspace_dir)
            if os.path.isdir(workspace_path):
                state_file = os.path.join(workspace_path, "state.vscdb")
                if os.path.exists(state_file):
                    try:
                        conn = sqlite3.connect(state_file)
                        cursor = conn.cursor()
                        
                        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%' OR value LIKE '%augment%'")
                        rows = cursor.fetchall()
                        
                        if rows:
                            augment_workspaces.append((workspace_dir, rows))
                        
                        conn.close()
                    except:
                        continue
        
        print(f"   找到 {len(augment_workspaces)} 个包含Augment数据的工作区:")
        for workspace_id, rows in augment_workspaces:
            print(f"   📁 工作区: {workspace_id}")
            for key, value in rows[:3]:  # 只显示前3条
                print(f"      {key}: {value[:100]}...")
            print()

if __name__ == "__main__":
    find_user_info()
