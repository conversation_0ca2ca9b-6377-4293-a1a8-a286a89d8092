#!/usr/bin/env python3
"""
快速测试当前系统功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

def test_current_functionality():
    """测试当前功能"""
    print("🔍 快速功能测试")
    print("=" * 40)
    
    # 1. 测试登录信息获取
    print("\n1️⃣ 测试登录信息获取")
    print("-" * 25)
    
    try:
        from utils.account_manager import get_current_login_info
        info = get_current_login_info()
        
        if info:
            email = info.get('email', '')
            username = info.get('username', '')
            cookie_length = len(info.get('cookie_string', ''))
            
            print(f"✅ 找到登录信息")
            print(f"📧 账号: {email}")
            print(f"👤 用户名: {username}")
            print(f"🍪 Cookie长度: {cookie_length} 字符")
            
            return True, info
        else:
            print("❌ 未找到登录信息")
            return False, None
            
    except Exception as e:
        print(f"❌ 获取登录信息失败: {e}")
        return False, None

def test_subscription_info(login_info):
    """测试订阅信息获取"""
    print("\n2️⃣ 测试订阅信息获取")
    print("-" * 25)
    
    if not login_info:
        print("⚠️ 没有登录信息，跳过订阅测试")
        return False, None
    
    try:
        from utils.account_manager import get_account_subscription_info
        
        success, sub_info = get_account_subscription_info()
        
        if success:
            plan_name = sub_info.get('plan_name', '未知')
            usage_count = sub_info.get('usage_count', 0)
            usage_limit = sub_info.get('usage_limit', 0)
            remaining_count = sub_info.get('remaining_count', 0)
            reset_date = sub_info.get('reset_date', '未知')
            
            print(f"✅ 订阅信息获取成功")
            print(f"📋 计划: {plan_name}")
            print(f"🔢 已使用: {usage_count}")
            print(f"📈 限制: {usage_limit}")
            print(f"⚡ 剩余: {remaining_count}")
            print(f"🔄 重置日期: {reset_date}")
            
            return True, sub_info
        else:
            error_msg = sub_info.get('error', '未知错误')
            print(f"❌ 订阅信息获取失败: {error_msg}")
            return False, sub_info
            
    except Exception as e:
        print(f"❌ 订阅信息获取异常: {e}")
        return False, None

def test_account_detection():
    """测试账号检测"""
    print("\n3️⃣ 测试账号检测")
    print("-" * 25)
    
    try:
        from utils.account_detector import get_all_account_info
        
        account_info = get_all_account_info()
        augment_account = account_info.get('augment_account', {})
        augment_activity = account_info.get('augment_activity', {})
        
        if augment_account.get('logged_in'):
            email = augment_account.get('email', '')
            username = augment_account.get('username', '')
            login_method = augment_account.get('login_method', '')
            
            print(f"✅ 检测到AugmentCode登录")
            print(f"📧 检测邮箱: {email}")
            print(f"👤 检测用户名: {username}")
            print(f"🔐 登录方式: {login_method}")
            
            if augment_activity.get('is_active'):
                print(f"🚀 检测到活动状态")
            else:
                print(f"😴 未检测到活动")
                
            return True, account_info
        else:
            print("❌ 未检测到AugmentCode登录")
            return False, account_info
            
    except Exception as e:
        print(f"❌ 账号检测失败: {e}")
        return False, None

def test_enhanced_methods(login_info):
    """测试增强方法"""
    print("\n4️⃣ 测试增强方法")
    print("-" * 25)
    
    if not login_info:
        print("⚠️ 没有登录信息，跳过增强方法测试")
        return False, None
    
    try:
        from enhanced_account_methods import get_enhanced_account_info
        
        cookie_string = login_info.get('cookie_string', '')
        if not cookie_string:
            print("❌ 没有Cookie信息")
            return False, None
        
        print(f"🔄 使用增强方法获取信息...")
        success, data = get_enhanced_account_info(cookie_string)
        
        if success:
            plan_name = data.get('plan_name', '未知')
            usage_count = data.get('usage_count', 0)
            usage_limit = data.get('usage_limit', 0)
            remaining_count = data.get('remaining_count', 0)
            reset_date = data.get('reset_date', '未知')
            
            print(f"✅ 增强方法获取成功")
            print(f"📋 计划: {plan_name}")
            print(f"🔢 已使用: {usage_count}")
            print(f"📈 限制: {usage_limit}")
            print(f"⚡ 剩余: {remaining_count}")
            print(f"🔄 重置日期: {reset_date}")
            
            return True, data
        else:
            error_msg = data.get('error', '未知错误')
            print(f"❌ 增强方法失败: {error_msg}")
            return False, data
            
    except ImportError:
        print("⚠️ 增强方法模块未找到")
        return False, None
    except Exception as e:
        print(f"❌ 增强方法异常: {e}")
        return False, None

def main():
    """主函数"""
    print("🎯 Free AugmentCode 功能测试")
    print("=" * 50)
    
    results = {}
    
    # 测试登录信息
    login_success, login_info = test_current_functionality()
    results['login'] = login_success
    
    # 测试订阅信息
    sub_success, sub_info = test_subscription_info(login_info)
    results['subscription'] = sub_success
    
    # 测试账号检测
    detect_success, detect_info = test_account_detection()
    results['detection'] = detect_success
    
    # 测试增强方法
    enhanced_success, enhanced_info = test_enhanced_methods(login_info)
    results['enhanced'] = enhanced_success
    
    # 总结
    print("\n📊 测试结果总结")
    print("=" * 40)
    
    total_tests = len(results)
    successful_tests = sum(results.values())
    
    print(f"✅ 登录信息: {'成功' if results['login'] else '失败'}")
    print(f"✅ 订阅信息: {'成功' if results['subscription'] else '失败'}")
    print(f"✅ 账号检测: {'成功' if results['detection'] else '失败'}")
    print(f"✅ 增强方法: {'成功' if results['enhanced'] else '失败'}")
    
    success_rate = (successful_tests / total_tests) * 100
    print(f"\n🎯 成功率: {success_rate:.1f}% ({successful_tests}/{total_tests})")
    
    if success_rate >= 75:
        status = "🟢 系统运行良好"
    elif success_rate >= 50:
        status = "🟡 部分功能可用"
    else:
        status = "🔴 需要修复"
    
    print(f"📈 状态: {status}")
    
    # 建议
    print(f"\n💡 建议")
    print("-" * 20)
    
    if not results['login']:
        print("• 需要先登录AugmentCode账号")
        print("• 运行: python gui.py 进行登录")
    elif not results['subscription']:
        print("• Cookie可能已失效，需要重新登录")
        print("• 检查网络连接")
    elif not results['detection']:
        print("• VS Code中的AugmentCode扩展可能未激活")
    
    if results['login'] and (results['subscription'] or results['enhanced']):
        print("• 系统可以获取账号信息！")
        print("• 运行: python gui.py 查看完整界面")
    
    return results

if __name__ == "__main__":
    main()
