#!/usr/bin/env python3
"""
工作区清理视图
扫描和清理工作区中的临时文件和缓存
"""

import os
import threading

import customtkinter as ctk
from tkinter import messagebox, filedialog

from utils.workspace_cleaner import WorkspaceCleaner


class CleanupView:
    """工作区清理视图类"""

    def __init__(self, parent_frame):
        """初始化清理视图

        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame
        self.cleaner = WorkspaceCleaner()
        self.scan_results = {}

        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="工作区清理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # 创建界面
        self._create_interface()

    def _create_interface(self):
        """创建界面"""
        # 说明区域
        info_frame = ctk.CTkFrame(self.main_frame)
        info_frame.pack(fill="x", pady=(0, 20))

        info_label = ctk.CTkLabel(
            info_frame,
            text="🧹 AugmentCode数据清理工具",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        info_label.pack(pady=(15, 5))

        desc_label = ctk.CTkLabel(
            info_frame,
            text="自动检测并清理AugmentCode相关的缓存、配置和临时文件，让您可以重新登录不同账号",
            font=ctk.CTkFont(size=12),
            text_color="gray",
            wraplength=600
        )
        desc_label.pack(pady=(0, 15))

        # 操作按钮区域
        button_frame = ctk.CTkFrame(self.main_frame)
        button_frame.pack(fill="x", pady=(0, 20))

        button_container = ctk.CTkFrame(button_frame, fg_color="transparent")
        button_container.pack(pady=20)

        self.scan_button = ctk.CTkButton(
            button_container,
            text="🔍 扫描AugmentCode数据",
            command=self._start_scan,
            height=40,
            width=200,
            fg_color="#2196F3",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.scan_button.pack(side="left", padx=(0, 15))

        self.clean_button = ctk.CTkButton(
            button_container,
            text="🧹 一键清理",
            command=self._start_cleanup,
            height=40,
            width=150,
            fg_color="#4CAF50",
            font=ctk.CTkFont(size=14, weight="bold"),
            state="disabled"
        )
        self.clean_button.pack(side="left", padx=(0, 15))

        self.backup_button = ctk.CTkButton(
            button_container,
            text="💾 备份数据",
            command=self._backup_data,
            height=40,
            width=120,
            fg_color="#FF9800",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.backup_button.pack(side="left")

        # 状态显示区域
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="请选择要扫描的目录",
            font=ctk.CTkFont(size=14)
        )
        self.status_label.pack(pady=15)

        # 结果显示区域
        self.results_frame = ctk.CTkFrame(self.main_frame)
        self.results_frame.pack(fill="both", expand=True)

        # 结果标题
        results_title = ctk.CTkLabel(
            self.results_frame,
            text="扫描结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))

        # 滚动区域
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.results_frame,
            height=300
        )
        self.scrollable_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # 初始提示
        self._show_initial_message()

    def _start_scan(self):
        """开始扫描AugmentCode数据"""
        # 禁用按钮
        self.scan_button.configure(state="disabled", text="扫描中...")
        self.clean_button.configure(state="disabled")
        self.backup_button.configure(state="disabled")

        # 更新状态
        self.status_label.configure(text="正在扫描AugmentCode相关数据...")

        # 清空之前的结果
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # 在后台线程中执行扫描
        threading.Thread(target=self._scan_worker, daemon=True).start()

    def _scan_worker(self):
        """扫描工作线程"""
        try:
            # 执行AugmentCode专用扫描
            results = self.cleaner.scan_augmentcode_data()
            self.scan_results = results

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_scan_results(results))

        except Exception as e:
            # 在主线程中显示错误
            self.parent_frame.after(0, lambda: self._show_scan_error(str(e)))

    def _backup_data(self):
        """备份AugmentCode数据"""
        try:
            backup_path = self.cleaner.backup_augmentcode_data()
            messagebox.showinfo(
                "备份完成",
                f"AugmentCode数据已备份到：\n{backup_path}"
            )
        except Exception as e:
            messagebox.showerror("备份失败", f"备份过程中发生错误：{str(e)}")

    def _update_scan_results(self, results):
        """更新扫描结果"""
        # 恢复按钮状态
        self.scan_button.configure(state="normal", text="🔍 开始扫描")

        if not results or not any(results.values()):
            self._show_no_results()
            return

        # 启用操作按钮
        self.clean_button.configure(state="normal")
        self.select_all_button.configure(state="normal")
        self.clear_all_button.configure(state="normal")

        # 统计信息
        total_files = sum(len(files) for files in results.values())
        total_size = sum(
            sum(os.path.getsize(file) for file in files if os.path.exists(file))
            for files in results.values()
        )

        self.status_label.configure(
            text=f"扫描完成：找到 {total_files} 个文件，总大小 {self._format_size(total_size)}"
        )

        # 显示结果
        self._display_results(results)

    def _display_results(self, results):
        """显示扫描结果"""
        self.checkboxes = {}

        for category, files in results.items():
            if not files:
                continue

            # 分类标题
            category_frame = ctk.CTkFrame(self.scrollable_frame)
            category_frame.pack(fill="x", pady=(10, 5))

            category_label = ctk.CTkLabel(
                category_frame,
                text=f"{category} ({len(files)} 个文件)",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            category_label.pack(pady=10)

            # 文件列表
            for file_path in files:
                if not os.path.exists(file_path):
                    continue

                file_frame = ctk.CTkFrame(self.scrollable_frame, fg_color=("gray90", "gray20"))
                file_frame.pack(fill="x", pady=2)

                # 复选框
                checkbox = ctk.CTkCheckBox(
                    file_frame,
                    text="",
                    width=20
                )
                checkbox.pack(side="left", padx=10, pady=8)

                # 文件信息
                file_size = os.path.getsize(file_path)
                file_info = f"{os.path.basename(file_path)} ({self._format_size(file_size)})"

                file_label = ctk.CTkLabel(
                    file_frame,
                    text=file_info,
                    font=ctk.CTkFont(size=12),
                    anchor="w"
                )
                file_label.pack(side="left", padx=(0, 10), pady=8, fill="x", expand=True)

                # 路径标签
                path_label = ctk.CTkLabel(
                    file_frame,
                    text=file_path,
                    font=ctk.CTkFont(size=10),
                    text_color="gray",
                    anchor="w"
                )
                path_label.pack(side="right", padx=10, pady=8)

                self.checkboxes[file_path] = checkbox

    def _show_initial_message(self):
        """显示初始消息"""
        message_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="选择目录并点击扫描开始清理工作区",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        message_label.pack(pady=50)

    def _show_no_results(self):
        """显示无结果消息"""
        self.status_label.configure(text="扫描完成：未找到需要清理的文件")

        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        message_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="✅ 工作区很干净，无需清理",
            font=ctk.CTkFont(size=16),
            text_color="#4CAF50"
        )
        message_label.pack(pady=50)

    def _show_scan_error(self, error_message):
        """显示扫描错误"""
        self.scan_button.configure(state="normal", text="🔍 开始扫描")
        self.status_label.configure(text=f"扫描失败：{error_message}")

        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        error_label = ctk.CTkLabel(
            self.scrollable_frame,
            text=f"❌ 扫描失败\n{error_message}",
            font=ctk.CTkFont(size=16),
            text_color="#F44336"
        )
        error_label.pack(pady=50)

    def _select_all(self):
        """全选"""
        for checkbox in self.checkboxes.values():
            checkbox.select()

    def _clear_all(self):
        """清空选择"""
        for checkbox in self.checkboxes.values():
            checkbox.deselect()

    def _start_cleanup(self):
        """开始清理"""
        selected_files = [
            file_path for file_path, checkbox in self.checkboxes.items()
            if checkbox.get()
        ]

        if not selected_files:
            messagebox.showwarning("警告", "请先选择要清理的文件")
            return

        # 确认对话框
        total_size = sum(
            os.path.getsize(file) for file in selected_files
            if os.path.exists(file)
        )

        result = messagebox.askyesno(
            "确认清理",
            f"确定要删除 {len(selected_files)} 个文件吗？\n"
            f"总大小：{self._format_size(total_size)}\n\n"
            "此操作不可撤销！"
        )

        if not result:
            return

        # 禁用按钮
        self.clean_button.configure(state="disabled", text="清理中...")
        self.scan_button.configure(state="disabled")

        # 在后台线程中执行清理
        threading.Thread(
            target=self._cleanup_worker,
            args=(selected_files,),
            daemon=True
        ).start()

    def _cleanup_worker(self, files_to_delete):
        """清理工作线程"""
        try:
            deleted_count = 0
            for file_path in files_to_delete:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_count += 1
                except Exception as e:
                    print(f"删除文件失败 {file_path}: {e}")

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._cleanup_completed(deleted_count, len(files_to_delete)))

        except Exception as e:
            # 在主线程中显示错误
            self.parent_frame.after(0, lambda: self._cleanup_error(str(e)))

    def _cleanup_completed(self, deleted_count, total_count):
        """清理完成"""
        self.clean_button.configure(state="normal", text="🧹 清理选中")
        self.scan_button.configure(state="normal")

        messagebox.showinfo(
            "清理完成",
            f"成功删除 {deleted_count}/{total_count} 个文件"
        )

        # 重新扫描
        if self.path_entry.get().strip():
            self._start_scan()

    def _cleanup_error(self, error_message):
        """清理错误"""
        self.clean_button.configure(state="normal", text="🧹 清理选中")
        self.scan_button.configure(state="normal")

        messagebox.showerror("清理失败", f"清理过程中发生错误：{error_message}")

    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"