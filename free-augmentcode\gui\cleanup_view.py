#!/usr/bin/env python3
"""
工作区清理视图
扫描和清理工作区中的临时文件和缓存
"""

import os
import threading

import customtkinter as ctk
from tkinter import messagebox

from utils.workspace_cleaner import WorkspaceCleaner


class CleanupView:
    """工作区清理视图类"""

    def __init__(self, parent_frame):
        """初始化清理视图

        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame
        self.cleaner = WorkspaceCleaner()
        self.scan_results = {}

        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="工作区清理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # 创建界面
        self._create_interface()

    def _create_interface(self):
        """创建界面"""
        # 说明区域
        info_frame = ctk.CTkFrame(self.main_frame)
        info_frame.pack(fill="x", pady=(0, 20))

        info_label = ctk.CTkLabel(
            info_frame,
            text="🧹 AugmentCode数据清理工具",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        info_label.pack(pady=(15, 5))

        desc_label = ctk.CTkLabel(
            info_frame,
            text="自动检测并清理AugmentCode相关的缓存、配置和临时文件，让您可以重新登录不同账号",
            font=ctk.CTkFont(size=12),
            text_color="gray",
            wraplength=600
        )
        desc_label.pack(pady=(0, 15))

        # 操作按钮区域
        button_frame = ctk.CTkFrame(self.main_frame)
        button_frame.pack(fill="x", pady=(0, 20))

        button_container = ctk.CTkFrame(button_frame, fg_color="transparent")
        button_container.pack(pady=20)

        self.scan_button = ctk.CTkButton(
            button_container,
            text="🔍 扫描AugmentCode数据",
            command=self._start_scan,
            height=40,
            width=200,
            fg_color="#2196F3",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.scan_button.pack(side="left", padx=(0, 15))

        self.clean_button = ctk.CTkButton(
            button_container,
            text="🧹 一键清理",
            command=self._start_cleanup,
            height=40,
            width=150,
            fg_color="#4CAF50",
            font=ctk.CTkFont(size=14, weight="bold"),
            state="disabled"
        )
        self.clean_button.pack(side="left", padx=(0, 15))

        self.backup_button = ctk.CTkButton(
            button_container,
            text="💾 备份数据",
            command=self._backup_data,
            height=40,
            width=120,
            fg_color="#FF9800",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.backup_button.pack(side="left")

        # 状态显示区域
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="请选择要扫描的目录",
            font=ctk.CTkFont(size=14)
        )
        self.status_label.pack(pady=15)

        # 结果显示区域
        self.results_frame = ctk.CTkFrame(self.main_frame)
        self.results_frame.pack(fill="both", expand=True)

        # 结果标题
        results_title = ctk.CTkLabel(
            self.results_frame,
            text="扫描结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))

        # 滚动区域
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.results_frame,
            height=300
        )
        self.scrollable_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # 初始提示
        self._show_initial_message()

    def _start_scan(self):
        """开始扫描AugmentCode数据"""
        # 禁用按钮
        self.scan_button.configure(state="disabled", text="扫描中...")
        self.clean_button.configure(state="disabled")
        self.backup_button.configure(state="disabled")

        # 更新状态
        self.status_label.configure(text="正在扫描AugmentCode相关数据...")

        # 清空之前的结果
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # 在后台线程中执行扫描
        threading.Thread(target=self._scan_worker, daemon=True).start()

    def _scan_worker(self):
        """扫描工作线程"""
        try:
            # 执行AugmentCode专用扫描
            results = self.cleaner.scan_augmentcode_data()
            self.scan_results = results

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_scan_results(results))

        except Exception as e:
            # 在主线程中显示错误
            self.parent_frame.after(0, lambda: self._show_scan_error(str(e)))

    def _backup_data(self):
        """备份AugmentCode数据"""
        try:
            backup_path = self.cleaner.backup_augmentcode_data()
            messagebox.showinfo(
                "备份完成",
                f"AugmentCode数据已备份到：\n{backup_path}"
            )
        except Exception as e:
            messagebox.showerror("备份失败", f"备份过程中发生错误：{str(e)}")

    def _update_scan_results(self, results):
        """更新扫描结果"""
        # 恢复按钮状态
        self.scan_button.configure(state="normal", text="🔍 扫描AugmentCode数据")
        self.backup_button.configure(state="normal")

        if not results or not any(results.values()):
            self._show_no_results()
            return

        # 启用清理按钮
        self.clean_button.configure(state="normal")

        # 统计信息
        total_files = sum(len(files) for files in results.values())

        self.status_label.configure(
            text=f"扫描完成：找到 {total_files} 个AugmentCode相关文件"
        )

        # 显示结果
        self._display_results(results)

    def _display_results(self, results):
        """显示扫描结果"""
        for category, files in results.items():
            if not files:
                continue

            # 分类标题
            category_frame = ctk.CTkFrame(self.scrollable_frame)
            category_frame.pack(fill="x", pady=(10, 5))

            # 根据类别设置颜色
            colors = {
                "Telemetry数据": "#FF9800",
                "数据库记录": "#F44336",
                "工作区存储": "#4CAF50",
                "配置文件": "#2196F3"
            }
            color = colors.get(category, "#9E9E9E")

            # 标题栏
            title_frame = ctk.CTkFrame(category_frame, fg_color=color, height=36)
            title_frame.pack(fill="x")

            category_label = ctk.CTkLabel(
                title_frame,
                text=f"🗂️ {category} ({len(files)} 个项目)",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="white"
            )
            category_label.pack(pady=8)

            # 内容区域
            content_frame = ctk.CTkFrame(category_frame, fg_color="transparent")
            content_frame.pack(fill="x", padx=15, pady=15)

            # 显示文件列表（最多显示前5个）
            display_files = files[:5]
            for i, file_path in enumerate(display_files):
                file_frame = ctk.CTkFrame(content_frame, fg_color=("gray90", "gray20"))
                file_frame.pack(fill="x", pady=2)

                # 文件信息
                if os.path.exists(file_path):
                    if os.path.isfile(file_path):
                        file_size = os.path.getsize(file_path)
                        file_info = f"📄 {os.path.basename(file_path)} ({self._format_size(file_size)})"
                    else:
                        file_info = f"📁 {os.path.basename(file_path)}"
                else:
                    file_info = f"❓ {os.path.basename(file_path)} (不存在)"

                file_label = ctk.CTkLabel(
                    file_frame,
                    text=file_info,
                    font=ctk.CTkFont(size=11),
                    anchor="w"
                )
                file_label.pack(side="left", padx=10, pady=6, fill="x", expand=True)

                # 路径标签
                path_label = ctk.CTkLabel(
                    file_frame,
                    text=file_path,
                    font=ctk.CTkFont(size=9),
                    text_color="gray",
                    anchor="w"
                )
                path_label.pack(side="right", padx=10, pady=6)

            # 如果文件数量超过5个，显示省略信息
            if len(files) > 5:
                more_label = ctk.CTkLabel(
                    content_frame,
                    text=f"... 还有 {len(files) - 5} 个项目",
                    font=ctk.CTkFont(size=10),
                    text_color="gray"
                )
                more_label.pack(pady=5)

    def _show_initial_message(self):
        """显示初始消息"""
        message_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="选择目录并点击扫描开始清理工作区",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        message_label.pack(pady=50)

    def _show_no_results(self):
        """显示无结果消息"""
        self.status_label.configure(text="扫描完成：未找到需要清理的文件")

        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        message_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="✅ 工作区很干净，无需清理",
            font=ctk.CTkFont(size=16),
            text_color="#4CAF50"
        )
        message_label.pack(pady=50)

    def _show_scan_error(self, error_message):
        """显示扫描错误"""
        self.scan_button.configure(state="normal", text="🔍 开始扫描")
        self.status_label.configure(text=f"扫描失败：{error_message}")

        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        error_label = ctk.CTkLabel(
            self.scrollable_frame,
            text=f"❌ 扫描失败\n{error_message}",
            font=ctk.CTkFont(size=16),
            text_color="#F44336"
        )
        error_label.pack(pady=50)

    def _select_all(self):
        """全选"""
        for checkbox in self.checkboxes.values():
            checkbox.select()

    def _clear_all(self):
        """清空选择"""
        for checkbox in self.checkboxes.values():
            checkbox.deselect()

    def _start_cleanup(self):
        """开始一键清理AugmentCode数据"""
        # 确认对话框
        result = messagebox.askyesno(
            "确认清理",
            "确定要清理所有AugmentCode相关数据吗？\n\n"
            "此操作将：\n"
            "• 修改设备ID和机器ID\n"
            "• 清理数据库中的AugmentCode记录\n"
            "• 清理工作区存储数据\n\n"
            "所有数据将自动备份，此操作不可撤销！"
        )

        if not result:
            return

        # 禁用按钮
        self.clean_button.configure(state="disabled", text="清理中...")
        self.scan_button.configure(state="disabled")
        self.backup_button.configure(state="disabled")

        # 在后台线程中执行清理
        threading.Thread(target=self._cleanup_worker, daemon=True).start()

    def _cleanup_worker(self):
        """清理工作线程"""
        try:
            results = {}

            # 1. 修改Telemetry ID
            try:
                telemetry_result = self.cleaner.modify_telemetry_ids()
                results['telemetry'] = telemetry_result
            except Exception as e:
                results['telemetry'] = {'error': str(e)}

            # 2. 清理数据库
            try:
                db_result = self.cleaner.clean_augment_database()
                results['database'] = db_result
            except Exception as e:
                results['database'] = {'error': str(e)}

            # 3. 清理工作区存储
            try:
                workspace_result = self.cleaner.clean_workspace_storage()
                results['workspace'] = workspace_result
            except Exception as e:
                results['workspace'] = {'error': str(e)}

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._cleanup_completed(results))

        except Exception as e:
            # 在主线程中显示错误
            self.parent_frame.after(0, lambda: self._cleanup_error(str(e)))

    def _cleanup_completed(self, results):
        """清理完成"""
        self.clean_button.configure(state="normal", text="🧹 一键清理")
        self.scan_button.configure(state="normal")
        self.backup_button.configure(state="normal")

        # 构建结果消息
        success_messages = []
        error_messages = []

        # Telemetry结果
        if 'telemetry' in results:
            if 'error' in results['telemetry']:
                error_messages.append(f"Telemetry修改失败: {results['telemetry']['error']}")
            else:
                success_messages.append("✅ 成功修改设备ID和机器ID")

        # 数据库结果
        if 'database' in results:
            if 'error' in results['database']:
                error_messages.append(f"数据库清理失败: {results['database']['error']}")
            else:
                deleted_rows = results['database'].get('deleted_rows', 0)
                success_messages.append(f"✅ 成功清理数据库记录 ({deleted_rows} 条)")

        # 工作区结果
        if 'workspace' in results:
            if 'error' in results['workspace']:
                error_messages.append(f"工作区清理失败: {results['workspace']['error']}")
            else:
                deleted_files = results['workspace'].get('deleted_files_count', 0)
                success_messages.append(f"✅ 成功清理工作区文件 ({deleted_files} 个)")

        # 显示结果
        if success_messages and not error_messages:
            message = "🎉 清理完成！\n\n" + "\n".join(success_messages) + "\n\n现在您可以重新启动VS Code并使用新邮箱登录AugmentCode。"
            messagebox.showinfo("清理完成", message)
        elif success_messages and error_messages:
            message = "⚠️ 部分清理完成\n\n成功:\n" + "\n".join(success_messages) + "\n\n失败:\n" + "\n".join(error_messages)
            messagebox.showwarning("部分完成", message)
        else:
            message = "❌ 清理失败\n\n" + "\n".join(error_messages)
            messagebox.showerror("清理失败", message)

        # 重新扫描
        self._start_scan()

    def _cleanup_error(self, error_message):
        """清理错误"""
        self.clean_button.configure(state="normal", text="🧹 一键清理")
        self.scan_button.configure(state="normal")
        self.backup_button.configure(state="normal")

        messagebox.showerror("清理失败", f"清理过程中发生错误：{error_message}")

    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"