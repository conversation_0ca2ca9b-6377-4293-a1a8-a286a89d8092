#!/usr/bin/env python3
"""
工作区清理视图
扫描和清理工作区中的临时文件和缓存
"""

import os
import threading

import customtkinter as ctk
from tkinter import messagebox, filedialog

from utils.workspace_cleaner import WorkspaceCleaner


class CleanupView:
    """工作区清理视图类"""

    def __init__(self, parent_frame):
        """初始化清理视图

        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame
        self.cleaner = WorkspaceCleaner()
        self.scan_results = {}

        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="工作区清理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # 创建界面
        self._create_interface()

    def _create_interface(self):
        """创建界面"""
        # 控制面板
        control_frame = ctk.CTkFrame(self.main_frame)
        control_frame.pack(fill="x", pady=(0, 20))

        # 路径选择区域
        path_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        path_frame.pack(fill="x", padx=15, pady=15)

        path_label = ctk.CTkLabel(
            path_frame,
            text="扫描路径:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        path_label.pack(side="left", padx=(0, 10))

        self.path_entry = ctk.CTkEntry(
            path_frame,
            placeholder_text="选择要扫描的目录...",
            height=32,
            width=400
        )
        self.path_entry.pack(side="left", padx=(0, 10), fill="x", expand=True)

        browse_button = ctk.CTkButton(
            path_frame,
            text="浏览",
            command=self._browse_directory,
            height=32,
            width=80
        )
        browse_button.pack(side="right", padx=(10, 0))

        # 操作按钮区域
        button_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=15, pady=(0, 15))

        self.scan_button = ctk.CTkButton(
            button_frame,
            text="🔍 开始扫描",
            command=self._start_scan,
            height=36,
            width=120,
            fg_color="#2196F3"
        )
        self.scan_button.pack(side="left", padx=(0, 10))

        self.clean_button = ctk.CTkButton(
            button_frame,
            text="🧹 清理选中",
            command=self._start_cleanup,
            height=36,
            width=120,
            fg_color="#4CAF50",
            state="disabled"
        )
        self.clean_button.pack(side="left", padx=(0, 10))

        self.select_all_button = ctk.CTkButton(
            button_frame,
            text="全选",
            command=self._select_all,
            height=36,
            width=80,
            state="disabled"
        )
        self.select_all_button.pack(side="left", padx=(0, 10))

        self.clear_all_button = ctk.CTkButton(
            button_frame,
            text="清空",
            command=self._clear_all,
            height=36,
            width=80,
            state="disabled"
        )
        self.clear_all_button.pack(side="left")

        # 状态显示区域
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="请选择要扫描的目录",
            font=ctk.CTkFont(size=14)
        )
        self.status_label.pack(pady=15)

        # 结果显示区域
        self.results_frame = ctk.CTkFrame(self.main_frame)
        self.results_frame.pack(fill="both", expand=True)

        # 结果标题
        results_title = ctk.CTkLabel(
            self.results_frame,
            text="扫描结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))

        # 滚动区域
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.results_frame,
            height=300
        )
        self.scrollable_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # 初始提示
        self._show_initial_message()

    def _browse_directory(self):
        """浏览目录"""
        directory = filedialog.askdirectory(
            title="选择要扫描的目录"
        )
        if directory:
            self.path_entry.delete(0, "end")
            self.path_entry.insert(0, directory)

    def _start_scan(self):
        """开始扫描"""
        scan_path = self.path_entry.get().strip()
        if not scan_path:
            messagebox.showwarning("警告", "请先选择要扫描的目录")
            return

        if not os.path.exists(scan_path):
            messagebox.showerror("错误", "选择的目录不存在")
            return

        # 禁用按钮
        self.scan_button.configure(state="disabled", text="扫描中...")
        self.clean_button.configure(state="disabled")
        self.select_all_button.configure(state="disabled")
        self.clear_all_button.configure(state="disabled")

        # 更新状态
        self.status_label.configure(text="正在扫描，请稍候...")

        # 清空之前的结果
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # 在后台线程中执行扫描
        threading.Thread(
            target=self._scan_worker,
            args=(scan_path,),
            daemon=True
        ).start()

    def _scan_worker(self, scan_path):
        """扫描工作线程"""
        try:
            # 执行扫描
            results = self.cleaner.scan_directory(scan_path)
            self.scan_results = results

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_scan_results(results))

        except Exception as e:
            # 在主线程中显示错误
            self.parent_frame.after(0, lambda: self._show_scan_error(str(e)))

    def _update_scan_results(self, results):
        """更新扫描结果"""
        # 恢复按钮状态
        self.scan_button.configure(state="normal", text="🔍 开始扫描")

        if not results or not any(results.values()):
            self._show_no_results()
            return

        # 启用操作按钮
        self.clean_button.configure(state="normal")
        self.select_all_button.configure(state="normal")
        self.clear_all_button.configure(state="normal")

        # 统计信息
        total_files = sum(len(files) for files in results.values())
        total_size = sum(
            sum(os.path.getsize(file) for file in files if os.path.exists(file))
            for files in results.values()
        )

        self.status_label.configure(
            text=f"扫描完成：找到 {total_files} 个文件，总大小 {self._format_size(total_size)}"
        )

        # 显示结果
        self._display_results(results)

    def _display_results(self, results):
        """显示扫描结果"""
        self.checkboxes = {}

        for category, files in results.items():
            if not files:
                continue

            # 分类标题
            category_frame = ctk.CTkFrame(self.scrollable_frame)
            category_frame.pack(fill="x", pady=(10, 5))

            category_label = ctk.CTkLabel(
                category_frame,
                text=f"{category} ({len(files)} 个文件)",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            category_label.pack(pady=10)

            # 文件列表
            for file_path in files:
                if not os.path.exists(file_path):
                    continue

                file_frame = ctk.CTkFrame(self.scrollable_frame, fg_color=("gray90", "gray20"))
                file_frame.pack(fill="x", pady=2)

                # 复选框
                checkbox = ctk.CTkCheckBox(
                    file_frame,
                    text="",
                    width=20
                )
                checkbox.pack(side="left", padx=10, pady=8)

                # 文件信息
                file_size = os.path.getsize(file_path)
                file_info = f"{os.path.basename(file_path)} ({self._format_size(file_size)})"

                file_label = ctk.CTkLabel(
                    file_frame,
                    text=file_info,
                    font=ctk.CTkFont(size=12),
                    anchor="w"
                )
                file_label.pack(side="left", padx=(0, 10), pady=8, fill="x", expand=True)

                # 路径标签
                path_label = ctk.CTkLabel(
                    file_frame,
                    text=file_path,
                    font=ctk.CTkFont(size=10),
                    text_color="gray",
                    anchor="w"
                )
                path_label.pack(side="right", padx=10, pady=8)

                self.checkboxes[file_path] = checkbox

    def _show_initial_message(self):
        """显示初始消息"""
        message_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="选择目录并点击扫描开始清理工作区",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        message_label.pack(pady=50)

    def _show_no_results(self):
        """显示无结果消息"""
        self.status_label.configure(text="扫描完成：未找到需要清理的文件")

        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        message_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="✅ 工作区很干净，无需清理",
            font=ctk.CTkFont(size=16),
            text_color="#4CAF50"
        )
        message_label.pack(pady=50)

    def _show_scan_error(self, error_message):
        """显示扫描错误"""
        self.scan_button.configure(state="normal", text="🔍 开始扫描")
        self.status_label.configure(text=f"扫描失败：{error_message}")

        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        error_label = ctk.CTkLabel(
            self.scrollable_frame,
            text=f"❌ 扫描失败\n{error_message}",
            font=ctk.CTkFont(size=16),
            text_color="#F44336"
        )
        error_label.pack(pady=50)

    def _select_all(self):
        """全选"""
        for checkbox in self.checkboxes.values():
            checkbox.select()

    def _clear_all(self):
        """清空选择"""
        for checkbox in self.checkboxes.values():
            checkbox.deselect()

    def _start_cleanup(self):
        """开始清理"""
        selected_files = [
            file_path for file_path, checkbox in self.checkboxes.items()
            if checkbox.get()
        ]

        if not selected_files:
            messagebox.showwarning("警告", "请先选择要清理的文件")
            return

        # 确认对话框
        total_size = sum(
            os.path.getsize(file) for file in selected_files
            if os.path.exists(file)
        )

        result = messagebox.askyesno(
            "确认清理",
            f"确定要删除 {len(selected_files)} 个文件吗？\n"
            f"总大小：{self._format_size(total_size)}\n\n"
            "此操作不可撤销！"
        )

        if not result:
            return

        # 禁用按钮
        self.clean_button.configure(state="disabled", text="清理中...")
        self.scan_button.configure(state="disabled")

        # 在后台线程中执行清理
        threading.Thread(
            target=self._cleanup_worker,
            args=(selected_files,),
            daemon=True
        ).start()

    def _cleanup_worker(self, files_to_delete):
        """清理工作线程"""
        try:
            deleted_count = 0
            for file_path in files_to_delete:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_count += 1
                except Exception as e:
                    print(f"删除文件失败 {file_path}: {e}")

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._cleanup_completed(deleted_count, len(files_to_delete)))

        except Exception as e:
            # 在主线程中显示错误
            self.parent_frame.after(0, lambda: self._cleanup_error(str(e)))

    def _cleanup_completed(self, deleted_count, total_count):
        """清理完成"""
        self.clean_button.configure(state="normal", text="🧹 清理选中")
        self.scan_button.configure(state="normal")

        messagebox.showinfo(
            "清理完成",
            f"成功删除 {deleted_count}/{total_count} 个文件"
        )

        # 重新扫描
        if self.path_entry.get().strip():
            self._start_scan()

    def _cleanup_error(self, error_message):
        """清理错误"""
        self.clean_button.configure(state="normal", text="🧹 清理选中")
        self.scan_button.configure(state="normal")

        messagebox.showerror("清理失败", f"清理过程中发生错误：{error_message}")

    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"