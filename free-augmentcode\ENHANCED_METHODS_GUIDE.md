# 增强的账号信息获取方法指南

## 🎯 概述

基于开源技术和最佳实践，我们开发了多种方法来获取AugmentCode账号信息，特别是解决订阅信息获取的问题。

## 🔧 技术方法

### 方法1: 直接API调用
**原理**: 尝试各种可能的API端点
**技术栈**: requests, HTTP headers manipulation
**优势**: 速度快，资源消耗低
**适用场景**: 网站提供公开或半公开API

```python
# 尝试的API端点
api_endpoints = [
    '/api/account',
    '/api/user', 
    '/api/subscription',
    '/api/account/subscription',
    '/api/user/subscription',
    '/api/me',
    '/api/profile',
    '/api/dashboard',
    '/api/usage',
    '/api/billing'
]
```

### 方法2: 增强页面抓取
**原理**: 使用多种解析技术从HTML页面提取信息
**技术栈**: requests, BeautifulSoup, 正则表达式
**优势**: 兼容性好，适用于大多数网站
**适用场景**: 信息显示在网页上

```python
# 解析模式
patterns = {
    'available': r'(\d+(?:\.\d+)?)\s+available',
    'usage': r'Used\s+(\d+)\s+of\s+(\d+)',
    'plan': r'(Community\s+Plan|Pro\s+Plan|Enterprise\s+Plan)',
    'date': r'(January|February|...|December)\s+\d+,\s+\d{4}'
}
```

### 方法3: 浏览器自动化
**原理**: 使用Selenium模拟真实浏览器行为
**技术栈**: Selenium WebDriver, Chrome/Firefox
**优势**: 可处理JavaScript渲染的内容
**适用场景**: 动态加载的页面，需要JavaScript执行

```python
# Chrome配置
chrome_options = Options()
chrome_options.add_argument('--headless')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')
```

### 方法4: 网络流量分析
**原理**: 分析网页中的网络请求模式，寻找隐藏的API
**技术栈**: 正则表达式, 网络请求分析
**优势**: 可发现隐藏的API端点
**适用场景**: 复杂的单页应用

## 🛡️ 反检测技术

### 1. User-Agent轮换
```python
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36...',
    # 更多User-Agent
]
```

### 2. 请求延迟
```python
import random
import time

# 随机延迟避免被检测
time.sleep(random.uniform(0.5, 2.0))
```

### 3. 会话管理
```python
session = requests.Session()
# 保持Cookie和会话状态
session.cookies.set(key, value)
```

### 4. 请求头模拟
```python
headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9...',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
}
```

## 📦 依赖安装

### 基础依赖
```bash
pip install requests beautifulsoup4 lxml selenium webdriver-manager
```

### 可选依赖（增强功能）
```bash
pip install fake-useragent undetected-chromedriver playwright httpx
```

### 自动安装
```bash
python install_enhanced_dependencies.py
```

## 🚀 使用方法

### 快速使用
```python
from enhanced_account_methods import get_enhanced_account_info

# 获取Cookie字符串（从现有登录信息）
cookie_string = "your_cookie_string_here"

# 获取账号信息
success, data = get_enhanced_account_info(cookie_string)

if success:
    print(f"计划: {data['plan_name']}")
    print(f"剩余次数: {data['remaining_count']}")
    print(f"使用情况: {data['usage_count']}/{data['usage_limit']}")
else:
    print(f"获取失败: {data['error']}")
```

### 集成到现有系统
```python
# 已自动集成到 utils/subscription_checker.py
from utils.account_manager import get_account_subscription_info

success, sub_info = get_account_subscription_info()
# 现在会自动尝试增强方法
```

## 🧪 测试和调试

### 运行测试
```bash
python test_enhanced_methods.py
```

### 调试网络请求
```bash
python debug_subscription_request.py
```

### 功能测试
```bash
python test_account_status_features.py
```

## 📊 成功率优化

### 方法优先级
1. **直接API调用** - 最快，资源消耗最低
2. **页面抓取** - 兼容性好，成功率高
3. **网络流量分析** - 可发现隐藏API
4. **浏览器自动化** - 最后手段，资源消耗大

### 错误处理
```python
try:
    success, data = method_1_direct_api(cookie_string)
    if success:
        return True, data
except Exception:
    pass

try:
    success, data = method_2_page_scraping(cookie_string)
    if success:
        return True, data
except Exception:
    pass

# 继续尝试其他方法...
```

## 🔍 故障排除

### 常见问题

1. **Cookie失效**
   - 重新登录获取新Cookie
   - 检查Cookie格式是否正确

2. **网络连接问题**
   - 检查网络连接
   - 尝试使用代理

3. **依赖缺失**
   - 运行 `install_enhanced_dependencies.py`
   - 手动安装缺失的包

4. **浏览器驱动问题**
   - 确保Chrome浏览器已安装
   - 更新WebDriver版本

### 调试技巧

1. **启用详细日志**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **保存响应内容**
```python
with open('response.html', 'w', encoding='utf-8') as f:
    f.write(response.text)
```

3. **检查Cookie格式**
```python
cookies = parse_cookie_string(cookie_string)
print(f"解析的Cookie: {cookies}")
```

## 🎯 最佳实践

### 1. 尊重网站政策
- 遵守robots.txt
- 不要过于频繁的请求
- 使用合理的延迟

### 2. 错误处理
- 实现重试机制
- 记录错误日志
- 提供降级方案

### 3. 性能优化
- 使用会话复用
- 缓存不变的数据
- 并行处理（谨慎使用）

### 4. 安全考虑
- 不要硬编码敏感信息
- 安全存储Cookie
- 定期更新依赖

## 📈 扩展性

### 添加新的解析模式
```python
new_patterns = {
    'custom_field': r'your_regex_pattern_here'
}
```

### 支持新的网站
```python
class CustomSiteGetter(EnhancedAccountInfoGetter):
    def __init__(self):
        super().__init__()
        self.base_url = "https://your-site.com"
```

### 添加新的方法
```python
def method_5_custom(self, cookie_string: str) -> Tuple[bool, Dict]:
    # 实现自定义方法
    pass
```

## 🤝 贡献

欢迎贡献新的方法和改进：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目基于开源技术开发，遵循相应的开源许可证。

---

**注意**: 请确保在使用这些方法时遵守相关网站的服务条款和法律法规。
