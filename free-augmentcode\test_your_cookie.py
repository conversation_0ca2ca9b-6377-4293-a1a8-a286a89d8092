#!/usr/bin/env python3
"""
测试您的具体Cookie
"""

from utils.cookie_login import create_cookie_login_manager, quick_validate_login
from utils.account_manager import save_login_info, get_current_login_info

def test_your_cookie():
    """测试您输入的Cookie"""
    print("🍪 测试您的Cookie")
    print("=" * 50)
    
    # 您的登录信息
    email = "<EMAIL>"
    cookie = "FK2LKbuSVja5O05rl3CBCsx5cmVoSiurRzGch%2FWypCB"
    
    print(f"📧 邮箱: {email}")
    print(f"🍪 Cookie: {cookie}")
    print(f"🍪 Cookie长度: {len(cookie)} 字符")
    
    # 创建Cookie管理器
    manager = create_cookie_login_manager()
    
    # 测试格式验证
    print(f"\n🔍 格式验证:")
    is_valid = manager.validate_cookie_format(cookie)
    print(f"  结果: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    # 测试解析
    print(f"\n🔧 Cookie解析:")
    parsed = manager.parse_cookie_string(cookie)
    print(f"  解析结果: {parsed}")
    
    # 测试登录验证
    print(f"\n🚀 登录验证:")
    success, message, user_info = manager.validate_augment_login(email, cookie)
    print(f"  验证结果: {'✅ 成功' if success else '❌ 失败'}")
    print(f"  消息: {message}")
    
    if success and user_info:
        print(f"  用户信息:")
        print(f"    - 邮箱: {user_info.get('email', 'N/A')}")
        print(f"    - 用户名: {user_info.get('username', 'N/A')}")
        print(f"    - Cookie数量: {user_info.get('cookie_count', 0)}")
        print(f"    - 登录时间: {user_info.get('login_time', 'N/A')}")
    
    # 测试快速验证
    print(f"\n⚡ 快速验证:")
    quick_success, quick_message = quick_validate_login(email, cookie)
    print(f"  结果: {'✅ 成功' if quick_success else '❌ 失败'}")
    print(f"  消息: {quick_message}")
    
    # 如果验证成功，尝试保存
    if quick_success:
        print(f"\n💾 保存登录信息:")
        save_success = save_login_info(email, cookie, email.split('@')[0])
        print(f"  保存结果: {'✅ 成功' if save_success else '❌ 失败'}")
        
        if save_success:
            # 验证保存的信息
            print(f"\n🔍 验证保存的信息:")
            saved_info = get_current_login_info()
            if saved_info:
                print(f"  ✅ 成功读取保存的信息:")
                print(f"    - 邮箱: {saved_info['email']}")
                print(f"    - 用户名: {saved_info['username']}")
                print(f"    - 最后使用: {saved_info['last_used']}")
                print(f"    - Cookie长度: {len(saved_info.get('cookie_string', ''))} 字符")
            else:
                print(f"  ❌ 无法读取保存的信息")

def main():
    """主函数"""
    try:
        test_your_cookie()
        print(f"\n" + "=" * 50)
        print("✅ 测试完成！")
        
        # 给出建议
        print(f"\n💡 建议:")
        print("1. 如果验证成功，您可以直接在GUI中使用这个Cookie")
        print("2. 如果验证失败，请检查Cookie是否完整或已过期")
        print("3. 建议从浏览器重新获取最新的Cookie")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
