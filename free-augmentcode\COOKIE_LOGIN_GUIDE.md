# 🍪 Cookie登录使用指南

## 📖 什么是Cookie登录？

Cookie登录是一种无需密码的登录方式，通过使用浏览器中保存的认证Cookie来验证身份。

### ✅ 优势
- **无需密码** - 只需要邮箱和Cookie
- **安全可靠** - 使用官方认证令牌
- **方便快捷** - 一次配置，长期使用

## 🔧 如何获取Cookie？

### 方法一：Chrome浏览器

1. **登录AugmentCode网站**
   - 在Chrome中访问 `https://augmentcode.com`
   - 使用您的账号正常登录

2. **打开开发者工具**
   - 按 `F12` 或右键选择"检查"
   - 点击 `Application` 标签页

3. **查找Cookie**
   - 在左侧找到 `Storage` → `Cookies`
   - 点击 `https://augmentcode.com`

4. **复制Cookie**
   - 选择所有Cookie行
   - 复制格式：`name1=value1; name2=value2; name3=value3`

### 方法二：Firefox浏览器

1. **登录AugmentCode网站**
2. **打开开发者工具** - 按 `F12`
3. **进入存储标签** - 点击 `Storage` 标签
4. **查看Cookie** - 展开 `Cookies` → 选择域名
5. **复制Cookie值**

### 方法三：Edge浏览器

1. **登录AugmentCode网站**
2. **打开开发者工具** - 按 `F12`
3. **应用程序标签** - 点击 `Application`
4. **Cookie部分** - 在左侧找到 `Cookies`
5. **复制所需Cookie**

## 📝 Cookie格式示例

### 正确格式
```
session=abc123def456; auth_token=xyz789; user_id=12345; csrf_token=abcdef
```

### 常见Cookie名称
- `session` / `sessionid` - 会话标识
- `auth` / `auth_token` - 认证令牌  
- `access_token` - 访问令牌
- `user` / `user_id` - 用户标识
- `csrf` / `csrftoken` - 安全令牌

## 🚀 在工具中使用

### 步骤1：启动GUI
```bash
python gui.py
```

### 步骤2：输入信息
1. **邮箱地址** - 输入您的AugmentCode账号邮箱
2. **Cookie信息** - 粘贴从浏览器复制的Cookie

### 步骤3：登录验证
- 点击"🚀 登录"按钮
- 系统会自动验证Cookie有效性
- 验证成功后保存登录信息

## ⚠️ 注意事项

### 安全提醒
- **保护Cookie隐私** - Cookie包含敏感认证信息
- **定期更新** - Cookie可能会过期，需要重新获取
- **安全环境** - 只在可信设备上使用

### 常见问题

**Q: Cookie会过期吗？**
A: 是的，Cookie通常有有效期。过期后需要重新登录获取新Cookie。

**Q: 可以在多个设备上使用同一个Cookie吗？**
A: 理论上可以，但可能会导致会话冲突。建议每个设备使用独立的Cookie。

**Q: 忘记密码怎么办？**
A: Cookie登录不需要密码，只要Cookie有效即可。如需重置密码，请访问官方网站。

**Q: Cookie验证失败怎么办？**
A: 可能原因：
- Cookie格式错误
- Cookie已过期
- 邮箱地址不匹配
- 网络连接问题

### 解决方案
1. **重新获取Cookie** - 重新登录浏览器获取最新Cookie
2. **检查格式** - 确保Cookie格式正确（key=value; key2=value2）
3. **验证邮箱** - 确保邮箱地址与Cookie对应的账号一致

## 🔄 更新Cookie

当Cookie过期时：
1. 重新在浏览器中登录AugmentCode
2. 获取新的Cookie
3. 在工具中点击"🔄 更换账号"
4. 输入新的Cookie信息

## 💡 高级技巧

### 批量管理
- 工具支持保存多个账号的Cookie
- 可以快速切换不同账号
- 自动记录最后使用时间

### 自动化
- Cookie验证成功后自动保存
- 下次启动自动加载已保存的账号
- 支持一键退出登录

## 📞 技术支持

如果遇到问题：
1. 查看工具日志输出
2. 运行 `python test_cookie_login.py` 进行诊断
3. 检查Cookie格式和有效性
4. 确认网络连接正常

---

**🎉 现在您可以享受无密码的便捷登录体验了！**
