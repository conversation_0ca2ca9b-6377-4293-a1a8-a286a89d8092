#!/usr/bin/env python3
"""
演示手动登录功能
展示如何使用GUI进行手动登录
"""

import time
from utils.account_manager import save_login_info, get_current_login_info, clear_login_info

def demo_manual_login():
    """演示手动登录流程"""
    print("🎯 AugmentCode 手动登录演示")
    print("=" * 60)
    
    print("\n📋 使用步骤:")
    print("1. 启动GUI应用程序")
    print("2. 进入'账号修改'页面")
    print("3. 使用以下任一方式输入登录信息:")
    print("   • 直接在页面中填写表单")
    print("   • 点击'⚡ 快速输入对话框'")
    print("   • 点击'✏️ 手动输入账号'")
    
    print("\n🍪 Cookie获取方法:")
    print("1. 登录 https://augmentcode.com")
    print("2. 按F12打开开发者工具")
    print("3. 切换到Network标签页")
    print("4. 刷新页面，找到任意请求")
    print("5. 在Request Headers中复制Cookie值")
    
    print("\n💡 示例数据:")
    print("邮箱: <EMAIL>")
    print("Cookie: session=abc123; user=user123; auth=token456")
    
    print("\n🔧 测试功能:")
    
    # 清除现有数据
    print("\n1. 清除现有登录数据...")
    clear_result = clear_login_info()
    print(f"   清除结果: {'✅ 成功' if clear_result else '❌ 失败'}")
    
    # 检查当前状态
    current_info = get_current_login_info()
    print(f"   当前状态: {'❌ 无登录信息' if current_info is None else '✅ 有登录信息'}")
    
    print("\n2. 现在您可以:")
    print("   • 运行 'python main.py' 启动GUI")
    print("   • 进入'账号修改'页面")
    print("   • 看到未登录状态的界面")
    print("   • 使用手动输入功能")
    
    print("\n3. 界面功能说明:")
    print("   🟢 '💾 保存登录信息' - 保存表单中的信息")
    print("   🔵 '⚡ 快速输入对话框' - 打开简化的输入对话框")
    print("   🟠 '❓ 获取帮助' - 查看Cookie获取指南")
    print("   🔄 '🔄 刷新状态' - 重新检测登录状态")
    
    print("\n4. 登录后的功能:")
    print("   🟢 '✏️ 手动输入账号' - 输入新账号")
    print("   🟠 '🔧 编辑当前账号' - 编辑现有账号")
    print("   🔴 '🗑️ 清除登录信息' - 清除所有登录数据")
    
    print("\n" + "=" * 60)
    print("🚀 现在请启动GUI进行测试！")
    print("   命令: python main.py")

def test_save_demo_account():
    """保存一个演示账号用于测试"""
    print("\n🧪 保存演示账号用于测试...")
    
    demo_email = "<EMAIL>"
    demo_username = "演示用户"
    demo_cookie = "session=demo_session_123; user=demo_user_456; auth=demo_auth_789; csrf=demo_csrf_abc"
    
    success = save_login_info(demo_email, demo_cookie, demo_username)
    
    if success:
        print(f"✅ 演示账号保存成功:")
        print(f"   邮箱: {demo_email}")
        print(f"   用户名: {demo_username}")
        print(f"   Cookie长度: {len(demo_cookie)} 字符")
        
        # 验证保存的信息
        saved_info = get_current_login_info()
        if saved_info:
            print(f"✅ 验证成功，现在GUI将显示已登录状态")
        else:
            print(f"❌ 验证失败")
    else:
        print(f"❌ 演示账号保存失败")

if __name__ == "__main__":
    demo_manual_login()
    
    print("\n" + "=" * 60)
    choice = input("是否保存演示账号用于测试GUI的已登录状态？(y/N): ").strip().lower()
    
    if choice == 'y':
        test_save_demo_account()
        print("\n💡 现在启动GUI将看到已登录状态，您可以测试编辑和清除功能")
    else:
        print("\n💡 现在启动GUI将看到未登录状态，您可以测试手动输入功能")
    
    print("\n🎉 演示完成！请运行 'python main.py' 启动GUI进行测试。")
