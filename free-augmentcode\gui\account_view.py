#!/usr/bin/env python3
"""
账号管理视图
显示和管理AugmentCode账号相关信息
"""

import threading

import customtkinter as ctk

from utils.account_detector import get_all_account_info


class AccountView:
    """账号管理视图类"""

    def __init__(self, parent_frame):
        """初始化账号管理视图

        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame

        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="账号管理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # 加载中标签
        self.loading_label = ctk.CTkLabel(
            self.main_frame,
            text="正在加载账号信息...",
            font=ctk.CTkFont(size=16)
        )
        self.loading_label.pack(pady=50)

        # 内容区域
        self.content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")

        # 加载数据
        threading.Thread(target=self._load_data, daemon=True).start()

    def _load_data(self):
        """加载数据"""
        try:
            # 获取所有账号数据
            account_info = get_all_account_info()

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_ui(account_info))

        except Exception as e:
            # 显示错误信息
            self.parent_frame.after(0, lambda: self._show_error(str(e)))

    def _update_ui(self, data):
        """更新UI

        Args:
            data: 加载的数据
        """
        # 隐藏加载标签
        self.loading_label.pack_forget()

        # 显示内容区域
        self.content_frame.pack(fill="both", expand=True)

        # 获取账号信息
        account_data = data.get('augment_account', {})

        # 根据登录状态显示不同内容
        if account_data.get('logged_in', False):
            self._create_logged_in_view(account_data)
        else:
            self._create_not_logged_in_view()

    def _create_logged_in_view(self, account_data):
        """创建已登录视图

        Args:
            account_data: 账号数据
        """
        # 账号信息区域
        info_frame = ctk.CTkFrame(self.content_frame)
        info_frame.pack(fill="x", pady=10)

        # 区域标题
        title_frame = ctk.CTkFrame(info_frame, fg_color="#4CAF50", height=36)
        title_frame.pack(fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text="✅ 当前账号信息",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="white"
        )
        title_label.pack(pady=5)

        # 账号详情
        details_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        details_frame.pack(fill="x", padx=15, pady=15)

        # 账号状态
        status_frame = ctk.CTkFrame(details_frame, fg_color=("gray95", "gray10"))
        status_frame.pack(fill="x", padx=5, pady=5)

        status_label = ctk.CTkLabel(
            status_frame,
            text="✅ 已登录状态",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#4CAF50"
        )
        status_label.pack(pady=10)

        # 账号信息表格
        info_table = ctk.CTkFrame(details_frame)
        info_table.pack(fill="x", pady=10)

        # 表头
        header_frame = ctk.CTkFrame(info_table, fg_color=("gray85", "gray20"), height=30)
        header_frame.pack(fill="x")

        header_label1 = ctk.CTkLabel(
            header_frame,
            text="信息项",
            font=ctk.CTkFont(size=12, weight="bold"),
            width=100
        )
        header_label1.pack(side="left", padx=10, pady=5)

        header_label2 = ctk.CTkLabel(
            header_frame,
            text="值",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        header_label2.pack(side="left", padx=10, pady=5)

        # 表格内容
        items = [
            ("账号邮箱", account_data.get('email', '未知邮箱')),
            ("用户名", account_data.get('username', '未知用户')),
            ("登录时间", account_data.get('login_time', '未知时间')),
            ("登录方式", account_data.get('login_method', '未知')),
            ("配置文件", account_data.get('config_path', '未知'))
        ]

        for i, (item, value) in enumerate(items):
            row_frame = ctk.CTkFrame(info_table, fg_color=("gray90", "gray15") if i % 2 == 0 else "transparent")
            row_frame.pack(fill="x")

            item_label = ctk.CTkLabel(
                row_frame,
                text=item,
                font=ctk.CTkFont(size=12),
                width=100
            )
            item_label.pack(side="left", padx=10, pady=8, anchor="w")

            value_label = ctk.CTkLabel(
                row_frame,
                text=value,
                font=ctk.CTkFont(size=12)
            )
            value_label.pack(side="left", padx=10, pady=8, fill="x")

        # 操作按钮区域
        button_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=20)

        refresh_button = ctk.CTkButton(
            button_frame,
            text="🔄 刷新状态",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            height=32,
            width=120
        )
        refresh_button.pack(side="left", padx=5)

    def _create_not_logged_in_view(self):
        """创建未登录视图"""
        not_logged_frame = ctk.CTkFrame(self.content_frame)
        not_logged_frame.pack(fill="both", expand=True, pady=50)

        icon_label = ctk.CTkLabel(
            not_logged_frame,
            text="👤",
            font=ctk.CTkFont(size=48)
        )
        icon_label.pack(pady=(30, 10))

        title_label = ctk.CTkLabel(
            not_logged_frame,
            text="未检测到登录账号",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)

        description_label = ctk.CTkLabel(
            not_logged_frame,
            text="系统中未检测到AugmentCode登录信息，请先登录您的账号",
            font=ctk.CTkFont(size=14),
            wraplength=500
        )
        description_label.pack(pady=10)

        hint_label = ctk.CTkLabel(
            not_logged_frame,
            text="请先在AugmentCode或VS Code中登录",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        hint_label.pack(pady=(0, 30))

        refresh_button = ctk.CTkButton(
            not_logged_frame,
            text="🔄 刷新状态",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            height=32,
            width=120
        )
        refresh_button.pack(pady=10)

    def _show_error(self, error_message):
        """显示错误信息

        Args:
            error_message: 错误信息
        """
        # 隐藏加载标签
        self.loading_label.pack_forget()

        # 显示错误信息
        error_frame = ctk.CTkFrame(self.main_frame)
        error_frame.pack(fill="both", expand=True, pady=50)

        icon_label = ctk.CTkLabel(
            error_frame,
            text="❌",
            font=ctk.CTkFont(size=48),
            text_color="#F44336"
        )
        icon_label.pack(pady=(30, 10))

        title_label = ctk.CTkLabel(
            error_frame,
            text="加载失败",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="#F44336"
        )
        title_label.pack(pady=10)

        description_label = ctk.CTkLabel(
            error_frame,
            text=f"错误信息: {error_message}",
            font=ctk.CTkFont(size=14),
            wraplength=500
        )
        description_label.pack(pady=10)

        refresh_button = ctk.CTkButton(
            error_frame,
            text="🔄 重试",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            height=32,
            width=120
        )
        refresh_button.pack(pady=10)