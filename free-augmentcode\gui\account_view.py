#!/usr/bin/env python3
"""
账号管理视图
显示和管理AugmentCode账号相关信息
"""

import threading
import tkinter.messagebox as messagebox

import customtkinter as ctk

from utils.account_detector import get_all_account_info
from utils.account_manager import save_login_info, get_current_login_info, clear_login_info


class AccountView:
    """账号管理视图类"""

    def __init__(self, parent_frame):
        """初始化账号管理视图

        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame

        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="账号管理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # 加载中标签
        self.loading_label = ctk.CTkLabel(
            self.main_frame,
            text="正在加载账号信息...",
            font=ctk.CTkFont(size=16)
        )
        self.loading_label.pack(pady=50)

        # 内容区域
        self.content_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")

        # 加载数据
        threading.Thread(target=self._load_data, daemon=True).start()

    def _load_data(self):
        """加载数据"""
        try:
            # 获取所有账号数据
            account_info = get_all_account_info()

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_ui(account_info))

        except Exception as e:
            # 显示错误信息
            self.parent_frame.after(0, lambda: self._show_error(str(e)))

    def _update_ui(self, data):
        """更新UI

        Args:
            data: 加载的数据
        """
        # 隐藏加载标签
        self.loading_label.pack_forget()

        # 显示内容区域
        self.content_frame.pack(fill="both", expand=True)

        # 获取账号信息
        account_data = data.get('augment_account', {})

        # 根据登录状态显示不同内容
        if account_data.get('logged_in', False):
            self._create_logged_in_view(account_data)
        else:
            self._create_not_logged_in_view()

    def _create_logged_in_view(self, account_data):
        """创建已登录视图

        Args:
            account_data: 账号数据
        """
        # 账号信息区域
        info_frame = ctk.CTkFrame(self.content_frame)
        info_frame.pack(fill="x", pady=10)

        # 区域标题
        title_frame = ctk.CTkFrame(info_frame, fg_color="#4CAF50", height=36)
        title_frame.pack(fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text="✅ 当前账号信息",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="white"
        )
        title_label.pack(pady=5)

        # 账号详情
        details_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        details_frame.pack(fill="x", padx=15, pady=15)

        # 账号状态
        status_frame = ctk.CTkFrame(details_frame, fg_color=("gray95", "gray10"))
        status_frame.pack(fill="x", padx=5, pady=5)

        status_label = ctk.CTkLabel(
            status_frame,
            text="✅ 已登录状态",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#4CAF50"
        )
        status_label.pack(pady=10)

        # 账号信息表格
        info_table = ctk.CTkFrame(details_frame)
        info_table.pack(fill="x", pady=10)

        # 表头
        header_frame = ctk.CTkFrame(info_table, fg_color=("gray85", "gray20"), height=30)
        header_frame.pack(fill="x")

        header_label1 = ctk.CTkLabel(
            header_frame,
            text="信息项",
            font=ctk.CTkFont(size=12, weight="bold"),
            width=100
        )
        header_label1.pack(side="left", padx=10, pady=5)

        header_label2 = ctk.CTkLabel(
            header_frame,
            text="值",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        header_label2.pack(side="left", padx=10, pady=5)

        # 检查是否有手动输入的登录信息
        manual_login_info = get_current_login_info()

        # 表格内容
        if manual_login_info:
            # 显示手动输入的信息
            items = [
                ("账号邮箱", manual_login_info.get('email', '未知邮箱')),
                ("用户名", manual_login_info.get('username', '未知用户')),
                ("登录时间", manual_login_info.get('last_used', '未知时间')),
                ("登录方式", "手动输入"),
                ("Cookie状态", "已保存" if manual_login_info.get('cookie_string') else "未保存")
            ]
        else:
            # 显示检测到的信息
            items = [
                ("账号邮箱", account_data.get('email', '未知邮箱')),
                ("用户名", account_data.get('username', '未知用户')),
                ("登录时间", account_data.get('login_time', '未知时间')),
                ("登录方式", account_data.get('login_method', '自动检测')),
                ("配置文件", account_data.get('config_path', '未知'))
            ]

        for i, (item, value) in enumerate(items):
            row_frame = ctk.CTkFrame(info_table, fg_color=("gray90", "gray15") if i % 2 == 0 else "transparent")
            row_frame.pack(fill="x")

            item_label = ctk.CTkLabel(
                row_frame,
                text=item,
                font=ctk.CTkFont(size=12),
                width=100
            )
            item_label.pack(side="left", padx=10, pady=8, anchor="w")

            value_label = ctk.CTkLabel(
                row_frame,
                text=value,
                font=ctk.CTkFont(size=12)
            )
            value_label.pack(side="left", padx=10, pady=8, fill="x")

        # 操作按钮区域
        button_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=20)

        refresh_button = ctk.CTkButton(
            button_frame,
            text="🔄 刷新状态",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            height=32,
            width=120
        )
        refresh_button.pack(side="left", padx=5)

        # 如果有手动输入的登录信息，显示清除按钮
        manual_login_info = get_current_login_info()
        if manual_login_info:
            clear_button = ctk.CTkButton(
                button_frame,
                text="🗑️ 清除登录信息",
                command=self._clear_manual_login,
                height=32,
                width=140,
                fg_color="red",
                hover_color="darkred"
            )
            clear_button.pack(side="left", padx=5)

    def _create_not_logged_in_view(self):
        """创建未登录视图"""
        # 创建滚动框架
        scroll_frame = ctk.CTkScrollableFrame(self.content_frame)
        scroll_frame.pack(fill="both", expand=True)

        # 状态显示区域
        status_frame = ctk.CTkFrame(scroll_frame)
        status_frame.pack(fill="x", padx=10, pady=10)

        icon_label = ctk.CTkLabel(
            status_frame,
            text="👤",
            font=ctk.CTkFont(size=48)
        )
        icon_label.pack(pady=(20, 10))

        title_label = ctk.CTkLabel(
            status_frame,
            text="未检测到登录账号",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)

        description_label = ctk.CTkLabel(
            status_frame,
            text="系统中未检测到AugmentCode登录信息，您可以手动输入账号信息",
            font=ctk.CTkFont(size=14),
            wraplength=500
        )
        description_label.pack(pady=10)

        # 手动登录区域
        login_frame = ctk.CTkFrame(scroll_frame)
        login_frame.pack(fill="x", padx=10, pady=10)

        login_title = ctk.CTkLabel(
            login_frame,
            text="手动输入登录信息",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        login_title.pack(pady=15)

        # 邮箱输入
        email_label = ctk.CTkLabel(login_frame, text="邮箱地址:")
        email_label.pack(anchor="w", padx=20, pady=(10, 5))

        self.email_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="请输入您的AugmentCode账号邮箱",
            width=400
        )
        self.email_entry.pack(padx=20, pady=(0, 10))

        # 用户名输入
        username_label = ctk.CTkLabel(login_frame, text="用户名:")
        username_label.pack(anchor="w", padx=20, pady=(10, 5))

        self.username_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="请输入您的用户名（可选）",
            width=400
        )
        self.username_entry.pack(padx=20, pady=(0, 10))

        # Cookie输入
        cookie_label = ctk.CTkLabel(login_frame, text="Cookie字符串:")
        cookie_label.pack(anchor="w", padx=20, pady=(10, 5))

        self.cookie_text = ctk.CTkTextbox(
            login_frame,
            height=100,
            width=400
        )
        self.cookie_text.pack(padx=20, pady=(0, 10))

        # Cookie说明
        cookie_help = ctk.CTkLabel(
            login_frame,
            text="提示：从浏览器中复制AugmentCode网站的Cookie信息\n登录 https://augmentcode.com 后，按F12打开开发者工具，\n在Network标签页找到请求头中的Cookie值",
            font=ctk.CTkFont(size=12),
            text_color="gray",
            justify="left"
        )
        cookie_help.pack(pady=5)

        # 操作按钮
        button_frame = ctk.CTkFrame(login_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=20)

        save_button = ctk.CTkButton(
            button_frame,
            text="💾 保存登录信息",
            command=self._save_manual_login,
            width=150,
            height=40
        )
        save_button.pack(side="left", padx=10)

        refresh_button = ctk.CTkButton(
            button_frame,
            text="🔄 刷新状态",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            width=120,
            height=40,
            fg_color="gray",
            hover_color="darkgray"
        )
        refresh_button.pack(side="left", padx=10)

        # 加载现有信息
        self._load_existing_manual_info()

    def _show_error(self, error_message):
        """显示错误信息

        Args:
            error_message: 错误信息
        """
        # 隐藏加载标签
        self.loading_label.pack_forget()

        # 显示错误信息
        error_frame = ctk.CTkFrame(self.main_frame)
        error_frame.pack(fill="both", expand=True, pady=50)

        icon_label = ctk.CTkLabel(
            error_frame,
            text="❌",
            font=ctk.CTkFont(size=48),
            text_color="#F44336"
        )
        icon_label.pack(pady=(30, 10))

        title_label = ctk.CTkLabel(
            error_frame,
            text="加载失败",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="#F44336"
        )
        title_label.pack(pady=10)

        description_label = ctk.CTkLabel(
            error_frame,
            text=f"错误信息: {error_message}",
            font=ctk.CTkFont(size=14),
            wraplength=500
        )
        description_label.pack(pady=10)

        refresh_button = ctk.CTkButton(
            error_frame,
            text="🔄 重试",
            command=lambda: threading.Thread(target=self._load_data, daemon=True).start(),
            height=32,
            width=120
        )
        refresh_button.pack(pady=10)

    def _load_existing_manual_info(self):
        """加载现有的手动登录信息"""
        try:
            login_info = get_current_login_info()
            if login_info and hasattr(self, 'email_entry'):
                self.email_entry.insert(0, login_info.get('email', ''))
                self.username_entry.insert(0, login_info.get('username', ''))
                self.cookie_text.insert("1.0", login_info.get('cookie_string', ''))
        except Exception as e:
            print(f"加载现有登录信息失败: {e}")

    def _save_manual_login(self):
        """保存手动输入的登录信息"""
        try:
            email = self.email_entry.get().strip()
            username = self.username_entry.get().strip()
            cookie_string = self.cookie_text.get("1.0", "end-1c").strip()

            if not email:
                messagebox.showerror("错误", "请输入邮箱地址")
                return

            if not cookie_string:
                messagebox.showerror("错误", "请输入Cookie字符串")
                return

            # 保存登录信息
            success = save_login_info(email, cookie_string, username)

            if success:
                messagebox.showinfo("成功", "登录信息已保存，正在刷新状态...")
                # 刷新界面
                threading.Thread(target=self._load_data, daemon=True).start()
            else:
                messagebox.showerror("错误", "保存登录信息失败")

        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def _clear_manual_login(self):
        """清除手动输入的登录信息"""
        try:
            result = messagebox.askyesno("确认", "确定要清除手动输入的登录信息吗？")
            if result:
                success = clear_login_info()
                if success:
                    messagebox.showinfo("成功", "登录信息已清除，正在刷新状态...")
                    # 刷新界面
                    threading.Thread(target=self._load_data, daemon=True).start()
                else:
                    messagebox.showerror("错误", "清除登录信息失败")
        except Exception as e:
            messagebox.showerror("错误", f"清除失败: {str(e)}")