#!/usr/bin/env python3
"""
测试Cookie登录功能
"""

from utils.cookie_login import create_cookie_login_manager, quick_validate_login
from utils.account_manager import save_login_info, get_current_login_info, get_account_manager

def test_cookie_validation():
    """测试Cookie验证功能"""
    print("🔍 测试Cookie验证功能")
    print("=" * 50)
    
    # 创建Cookie管理器
    manager = create_cookie_login_manager()
    
    # 测试不同的Cookie格式
    test_cases = [
        {
            "email": "<EMAIL>",
            "cookie": "session=abc123; auth_token=def456; user_id=789",
            "description": "标准格式Cookie"
        },
        {
            "email": "<EMAIL>", 
            "cookie": "sessionid=xyz789; csrftoken=abc123",
            "description": "Django风格Cookie"
        },
        {
            "email": "<EMAIL>",
            "cookie": "JSESSIONID=**********; XSRF-TOKEN=abcdef",
            "description": "Java风格Cookie"
        },
        {
            "email": "<EMAIL>",
            "cookie": "just_some_random_text",
            "description": "无效格式"
        },
        {
            "email": "<EMAIL>",
            "cookie": "",
            "description": "空Cookie"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {case['description']}")
        print(f"邮箱: {case['email']}")
        print(f"Cookie: {case['cookie'][:50]}{'...' if len(case['cookie']) > 50 else ''}")
        
        success, message, user_info = manager.validate_augment_login(
            case['email'], 
            case['cookie']
        )
        
        status = "✅ 成功" if success else "❌ 失败"
        print(f"结果: {status} - {message}")
        
        if success and user_info:
            print(f"用户信息: {user_info.get('username', 'N/A')}")
            print(f"Cookie数量: {user_info.get('cookie_count', 0)}")

def test_account_management():
    """测试账号管理功能"""
    print("\n" + "=" * 50)
    print("🔍 测试账号管理功能")
    
    # 测试保存登录信息
    test_email = "<EMAIL>"
    test_cookie = "session=test123; auth=token456; user=testuser"
    
    print(f"\n保存测试账号: {test_email}")
    success = save_login_info(test_email, test_cookie, "TestUser")
    print(f"保存结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 检查当前登录信息
    print("\n检查当前登录信息:")
    current = get_current_login_info()
    if current:
        print(f"✅ 找到登录信息:")
        print(f"  邮箱: {current['email']}")
        print(f"  用户名: {current['username']}")
        print(f"  最后使用: {current['last_used']}")
        print(f"  Cookie长度: {len(current.get('cookie_string', ''))} 字符")
    else:
        print("❌ 未找到登录信息")
    
    # 测试账号管理器
    print("\n测试账号管理器:")
    manager = get_account_manager()
    summary = manager.get_account_summary()
    
    print(f"总账号数: {summary['total_accounts']}")
    print(f"有活跃账号: {summary['has_active_account']}")
    print(f"活跃邮箱: {summary['active_email']}")

def test_quick_validation():
    """测试快速验证功能"""
    print("\n" + "=" * 50)
    print("🔍 测试快速验证功能")
    
    test_cases = [
        ("<EMAIL>", "session=abc; token=def"),
        ("<EMAIL>", "invalid_format"),
        ("", "session=abc; token=def"),
        ("<EMAIL>", "")
    ]
    
    for email, cookie in test_cases:
        print(f"\n测试: {email} | {cookie[:20]}{'...' if len(cookie) > 20 else ''}")
        success, message = quick_validate_login(email, cookie)
        status = "✅ 成功" if success else "❌ 失败"
        print(f"结果: {status} - {message}")

def main():
    """主测试函数"""
    print("🚀 Cookie登录功能测试")
    print("=" * 50)
    
    try:
        # 测试Cookie验证
        test_cookie_validation()
        
        # 测试账号管理
        test_account_management()
        
        # 测试快速验证
        test_quick_validation()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
