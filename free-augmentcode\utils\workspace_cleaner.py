#!/usr/bin/env python3
"""
AugmentCode数据清理器
专门用于清理AugmentCode相关的配置、缓存和临时文件
"""

import os
import json
import sqlite3
import shutil
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List


class WorkspaceCleaner:
    """AugmentCode数据清理器类"""

    def __init__(self):
        """初始化清理器"""
        # AugmentCode相关路径
        self.augmentcode_paths = self._get_augmentcode_paths()

        # 要清理的数据类型
        self.cleanup_categories = {
            "VS Code配置": {
                "description": "VS Code中的AugmentCode相关配置",
                "paths": [],
                "files": []
            },
            "扩展数据": {
                "description": "AugmentCode扩展的数据文件",
                "paths": [],
                "files": []
            },
            "工作区存储": {
                "description": "工作区中的AugmentCode存储数据",
                "paths": [],
                "files": []
            },
            "数据库记录": {
                "description": "SQLite数据库中的AugmentCode记录",
                "paths": [],
                "files": []
            },
            "Telemetry数据": {
                "description": "设备ID和机器ID相关数据",
                "paths": [],
                "files": []
            }
        }

    def _get_augmentcode_paths(self):
        """获取AugmentCode相关路径"""
        home = Path.home()
        paths = {
            "vscode_config": [],
            "vscode_extensions": [],
            "workspace_storage": [],
            "telemetry": []
        }

        # Windows路径
        if os.name == 'nt':
            appdata = home / "AppData" / "Roaming"
            paths["vscode_config"].extend([
                appdata / "Code" / "User",
                appdata / "Code" / "CachedExtensions"
            ])
            paths["vscode_extensions"].extend([
                appdata / "Code" / "User" / "extensions",
                home / ".vscode" / "extensions"
            ])
            paths["workspace_storage"].extend([
                appdata / "Code" / "User" / "workspaceStorage"
            ])
            paths["telemetry"].extend([
                appdata / "Code" / "User" / "globalStorage",
                appdata / "Code" / "logs"
            ])

        # macOS路径
        elif os.name == 'posix' and 'darwin' in os.uname().sysname.lower():
            library = home / "Library" / "Application Support"
            paths["vscode_config"].extend([
                library / "Code" / "User"
            ])
            paths["vscode_extensions"].extend([
                library / "Code" / "User" / "extensions",
                home / ".vscode" / "extensions"
            ])
            paths["workspace_storage"].extend([
                library / "Code" / "User" / "workspaceStorage"
            ])
            paths["telemetry"].extend([
                library / "Code" / "User" / "globalStorage",
                library / "Code" / "logs"
            ])

        # Linux路径
        else:
            config = home / ".config"
            paths["vscode_config"].extend([
                config / "Code" / "User"
            ])
            paths["vscode_extensions"].extend([
                config / "Code" / "User" / "extensions",
                home / ".vscode" / "extensions"
            ])
            paths["workspace_storage"].extend([
                config / "Code" / "User" / "workspaceStorage"
            ])
            paths["telemetry"].extend([
                config / "Code" / "User" / "globalStorage",
                config / "Code" / "logs"
            ])

        return paths
    
    def scan_directory(self, directory_path: str) -> Dict[str, List[str]]:
        """扫描目录，查找可清理的文件
        
        Args:
            directory_path: 要扫描的目录路径
            
        Returns:
            Dict[str, List[str]]: 按类别分组的文件列表
        """
        results = {}
        directory_path = Path(directory_path)
        
        if not directory_path.exists():
            return results
        
        for category, patterns in self.cleanup_patterns.items():
            files = []
            
            for pattern in patterns:
                try:
                    # 使用glob查找匹配的文件
                    matches = list(directory_path.glob(pattern))
                    
                    for match in matches:
                        if match.is_file():
                            # 检查文件是否在排除目录中
                            if not self._is_in_excluded_dir(match, directory_path):
                                files.append(str(match))
                        elif match.is_dir() and pattern.endswith("/**/*"):
                            # 对于目录模式，添加目录中的所有文件
                            for file_path in self._get_files_in_dir(match):
                                if not self._is_in_excluded_dir(Path(file_path), directory_path):
                                    files.append(file_path)
                
                except Exception as e:
                    print(f"扫描模式 {pattern} 时出错: {e}")
                    continue
            
            if files:
                results[category] = sorted(list(set(files)))
        
        return results
    
    def _is_in_excluded_dir(self, file_path: Path, base_path: Path) -> bool:
        """检查文件是否在排除的目录中
        
        Args:
            file_path: 文件路径
            base_path: 基础路径
            
        Returns:
            bool: 是否在排除目录中
        """
        try:
            relative_path = file_path.relative_to(base_path)
            parts = relative_path.parts
            
            # 检查路径的任何部分是否在排除列表中
            for part in parts:
                if part in self.exclude_dirs:
                    return True
            
            return False
        except ValueError:
            # 如果文件不在基础路径下，则排除
            return True
    
    def _get_files_in_dir(self, directory: Path) -> List[str]:
        """获取目录中的所有文件
        
        Args:
            directory: 目录路径
            
        Returns:
            List[str]: 文件路径列表
        """
        files = []
        
        try:
            if directory.is_dir():
                for item in directory.rglob("*"):
                    if item.is_file():
                        files.append(str(item))
        except Exception as e:
            print(f"遍历目录 {directory} 时出错: {e}")
        
        return files
    
    def clean_files(self, file_paths: List[str]) -> Dict[str, int]:
        """清理指定的文件
        
        Args:
            file_paths: 要删除的文件路径列表
            
        Returns:
            Dict[str, int]: 清理结果统计
        """
        results = {
            "deleted": 0,
            "failed": 0,
            "total": len(file_paths)
        }
        
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        results["deleted"] += 1
                    elif os.path.isdir(file_path):
                        # 对于目录，尝试删除（如果为空）
                        try:
                            os.rmdir(file_path)
                            results["deleted"] += 1
                        except OSError:
                            # 目录不为空，跳过
                            results["failed"] += 1
                else:
                    # 文件已经不存在
                    results["deleted"] += 1
            except Exception as e:
                print(f"删除文件 {file_path} 失败: {e}")
                results["failed"] += 1
        
        return results
    
    def get_directory_size(self, directory_path: str) -> int:
        """获取目录大小
        
        Args:
            directory_path: 目录路径
            
        Returns:
            int: 目录大小（字节）
        """
        total_size = 0
        directory_path = Path(directory_path)
        
        try:
            for file_path in directory_path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            print(f"计算目录大小时出错: {e}")
        
        return total_size
    
    def get_file_size(self, file_path: str) -> int:
        """获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 文件大小（字节）
        """
        try:
            return os.path.getsize(file_path)
        except Exception:
            return 0
    
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化的大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def is_safe_to_delete(self, file_path: str) -> bool:
        """检查文件是否安全删除
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否安全删除
        """
        file_path = Path(file_path)
        
        # 检查是否是重要的系统文件
        important_files = {
            "requirements.txt",
            "package.json",
            "setup.py",
            "Dockerfile",
            "docker-compose.yml",
            ".gitignore",
            "README.md",
            "LICENSE"
        }
        
        if file_path.name.lower() in important_files:
            return False
        
        # 检查是否在重要目录中
        important_dirs = {
            "src",
            "source",
            "lib",
            "include",
            "bin"
        }
        
        for part in file_path.parts:
            if part.lower() in important_dirs:
                return False
        
        return True
