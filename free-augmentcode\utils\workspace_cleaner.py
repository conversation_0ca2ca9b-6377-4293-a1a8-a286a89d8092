#!/usr/bin/env python3
"""
AugmentCode数据清理器
基于原项目功能，专门用于清理AugmentCode相关的配置、缓存和临时文件
"""

import os
import json
import sqlite3
import shutil
import time
import zipfile
import stat
import sys
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List


class WorkspaceCleaner:
    """AugmentCode数据清理器类"""

    def __init__(self):
        """初始化清理器"""
        # 初始化路径
        self.paths = self._get_vscode_paths()

    def _get_vscode_paths(self):
        """获取VS Code相关路径"""
        home = Path.home()

        if sys.platform == "win32":
            # Windows
            appdata = os.getenv("APPDATA", "")
            return {
                "storage_json": os.path.join(appdata, "Code", "User", "globalStorage", "storage.json"),
                "state_db": os.path.join(appdata, "Code", "User", "globalStorage", "state.vscdb"),
                "machine_id": os.path.join(appdata, "Code", "User", "machineid"),
                "workspace_storage": os.path.join(appdata, "Code", "User", "workspaceStorage"),
                "global_storage": os.path.join(appdata, "Code", "User", "globalStorage")
            }
        elif sys.platform == "darwin":
            # macOS
            return {
                "storage_json": os.path.join(str(home), "Library", "Application Support", "Code", "User", "globalStorage", "storage.json"),
                "state_db": os.path.join(str(home), "Library", "Application Support", "Code", "User", "globalStorage", "state.vscdb"),
                "machine_id": os.path.join(str(home), "Library", "Application Support", "Code", "machineid"),
                "workspace_storage": os.path.join(str(home), "Library", "Application Support", "Code", "User", "workspaceStorage"),
                "global_storage": os.path.join(str(home), "Library", "Application Support", "Code", "User", "globalStorage")
            }
        else:
            # Linux
            return {
                "storage_json": os.path.join(str(home), ".config", "Code", "User", "globalStorage", "storage.json"),
                "state_db": os.path.join(str(home), ".config", "Code", "User", "globalStorage", "state.vscdb"),
                "machine_id": os.path.join(str(home), ".config", "Code", "User", "machineid"),
                "workspace_storage": os.path.join(str(home), ".config", "Code", "User", "workspaceStorage"),
                "global_storage": os.path.join(str(home), ".config", "Code", "User", "globalStorage")
            }

    def _create_backup(self, file_path: str) -> str:
        """创建文件备份"""
        timestamp = int(time.time())
        backup_path = f"{file_path}.bak.{timestamp}"
        shutil.copy2(file_path, backup_path)
        return backup_path

    def _generate_machine_id(self) -> str:
        """生成新的机器ID"""
        return str(uuid.uuid4())

    def _generate_device_id(self) -> str:
        """生成新的设备ID"""
        return str(uuid.uuid4())

    def scan_augmentcode_data(self) -> Dict[str, List[str]]:
        """扫描AugmentCode相关数据"""
        results = {
            "Telemetry数据": [],
            "数据库记录": [],
            "工作区存储": [],
            "配置文件": []
        }

        # 检查storage.json
        if os.path.exists(self.paths["storage_json"]):
            results["Telemetry数据"].append(self.paths["storage_json"])

        # 检查machine ID文件
        if os.path.exists(self.paths["machine_id"]):
            results["Telemetry数据"].append(self.paths["machine_id"])

        # 检查数据库
        if os.path.exists(self.paths["state_db"]):
            results["数据库记录"].append(self.paths["state_db"])

        # 检查工作区存储
        workspace_path = Path(self.paths["workspace_storage"])
        if workspace_path.exists():
            for item in workspace_path.rglob("*"):
                if item.is_file():
                    results["工作区存储"].append(str(item))

        # 检查全局存储中的AugmentCode相关文件
        global_storage_path = Path(self.paths["global_storage"])
        if global_storage_path.exists():
            for item in global_storage_path.rglob("*"):
                if item.is_file() and "augment" in item.name.lower():
                    results["配置文件"].append(str(item))

        return results

    def modify_telemetry_ids(self) -> dict:
        """修改Telemetry ID"""
        storage_path = self.paths["storage_json"]
        machine_id_path = self.paths["machine_id"]

        if not os.path.exists(storage_path):
            raise FileNotFoundError(f"Storage文件未找到: {storage_path}")

        # 创建备份
        storage_backup_path = self._create_backup(storage_path)
        machine_id_backup_path = None
        if os.path.exists(machine_id_path):
            machine_id_backup_path = self._create_backup(machine_id_path)

        # 读取当前JSON内容
        with open(storage_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 保存旧值
        old_machine_id = data.get('telemetry.machineId', '')
        old_device_id = data.get('telemetry.devDeviceId', '')

        # 生成新ID
        new_machine_id = self._generate_machine_id()
        new_device_id = self._generate_device_id()

        # 更新storage.json中的值
        data['telemetry.machineId'] = new_machine_id
        data['telemetry.devDeviceId'] = new_device_id

        # 写回storage.json
        with open(storage_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4)

        # 写入新的机器ID到机器ID文件
        with open(machine_id_path, 'w', encoding='utf-8') as f:
            f.write(new_device_id)

        return {
            'old_machine_id': old_machine_id,
            'new_machine_id': new_machine_id,
            'old_device_id': old_device_id,
            'new_device_id': new_device_id,
            'storage_backup_path': storage_backup_path,
            'machine_id_backup_path': machine_id_backup_path
        }

    def clean_augment_database(self) -> dict:
        """清理数据库中的AugmentCode记录"""
        db_path = self.paths["state_db"]

        if not os.path.exists(db_path):
            raise FileNotFoundError(f"数据库文件未找到: {db_path}")

        # 创建备份
        db_backup_path = self._create_backup(db_path)

        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            # 执行删除查询
            cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
            deleted_rows = cursor.rowcount

            # 提交更改
            conn.commit()

            return {
                'db_backup_path': db_backup_path,
                'deleted_rows': deleted_rows
            }
        finally:
            cursor.close()
            conn.close()

    def remove_readonly(self, func, path, excinfo):
        """处理只读文件和目录的删除"""
        try:
            os.chmod(path, stat.S_IWRITE)
            func(path)
        except Exception:
            return False
        return True

    def force_delete_directory(self, path: Path) -> bool:
        """强制删除目录及其所有内容"""
        try:
            if os.name == 'nt':
                # Windows处理只读文件并使用长路径
                path_str = '\\\\?\\' + str(path.resolve())
                shutil.rmtree(path_str, onerror=self.remove_readonly)
            else:
                shutil.rmtree(path, onerror=self.remove_readonly)
            return True
        except Exception:
            return False

    def clean_workspace_storage(self) -> dict:
        """清理工作区存储"""
        workspace_path = self.paths["workspace_storage"]

        if not os.path.exists(workspace_path):
            raise FileNotFoundError(f"工作区存储目录未找到: {workspace_path}")

        # 转换为Path对象
        workspace_path = Path(workspace_path)

        # 创建带时间戳的备份文件名
        timestamp = int(time.time())
        backup_path = f"{workspace_path}_backup_{timestamp}.zip"

        # 创建zip备份
        failed_compressions = []
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in workspace_path.rglob('*'):
                if file_path.is_file():
                    try:
                        file_path_str = str(file_path)
                        if os.name == 'nt':
                            file_path_str = '\\\\?\\' + str(file_path.resolve())

                        arcname = file_path.relative_to(workspace_path)
                        zipf.write(file_path_str, str(arcname))
                    except (OSError, PermissionError, zipfile.BadZipFile) as e:
                        failed_compressions.append({
                            'file': str(file_path),
                            'error': str(e)
                        })
                        continue

        # 删除前计算文件数量
        total_files = sum(1 for _ in workspace_path.rglob('*') if _.is_file())

        # 删除目录中的所有文件
        failed_operations = []

        def handle_error(e: Exception, path: Path, item_type: str):
            failed_operations.append({
                'type': item_type,
                'path': str(path),
                'error': str(e)
            })

        # 首先尝试一次性删除整个目录树
        if not self.force_delete_directory(workspace_path):
            # 如果批量删除失败，尝试逐个文件删除
            # 先删除文件
            for file_path in workspace_path.rglob('*'):
                if file_path.is_file():
                    try:
                        # 如果存在只读属性则清除
                        if os.name == 'nt':
                            file_path_str = '\\\\?\\' + str(file_path.resolve())
                            os.chmod(file_path_str, stat.S_IWRITE)
                        else:
                            os.chmod(str(file_path), stat.S_IWRITE)

                        file_path.unlink(missing_ok=True)
                    except (OSError, PermissionError) as e:
                        handle_error(e, file_path, 'file')

            # 从最深层到根目录删除目录
            dirs_to_delete = sorted(
                [p for p in workspace_path.rglob('*') if p.is_dir()],
                key=lambda x: len(str(x).split(os.sep)),
                reverse=True
            )

            for dir_path in dirs_to_delete:
                try:
                    # 先尝试强制删除
                    if not self.force_delete_directory(dir_path):
                        # 如果强制删除失败，尝试常规删除
                        if os.name == 'nt':
                            dir_path_str = '\\\\?\\' + str(dir_path.resolve())
                            os.rmdir(dir_path_str)
                        else:
                            dir_path.rmdir()
                except (OSError, PermissionError) as e:
                    handle_error(e, dir_path, 'directory')

        return {
            'backup_path': str(backup_path),
            'deleted_files_count': total_files,
            'failed_operations': failed_operations,
            'failed_compressions': failed_compressions
        }

    def backup_augmentcode_data(self) -> str:
        """备份所有AugmentCode数据"""
        timestamp = int(time.time())
        backup_dir = f"augmentcode_backup_{timestamp}"
        os.makedirs(backup_dir, exist_ok=True)

        # 备份各种文件
        backup_files = []

        for name, path in self.paths.items():
            if os.path.exists(path):
                if os.path.isfile(path):
                    backup_file = os.path.join(backup_dir, f"{name}_{os.path.basename(path)}")
                    shutil.copy2(path, backup_file)
                    backup_files.append(backup_file)
                elif os.path.isdir(path):
                    backup_subdir = os.path.join(backup_dir, name)
                    shutil.copytree(path, backup_subdir, ignore_errors=True)
                    backup_files.append(backup_subdir)

        return backup_dir
