# AugmentCode Cookie 获取指南

本指南将帮助您获取AugmentCode网站的Cookie信息，以便在本工具中手动输入登录信息。

## 🍪 什么是Cookie？

Cookie是网站存储在您浏览器中的小型数据文件，用于记住您的登录状态和偏好设置。通过复制Cookie，我们可以模拟您的登录状态。

## 📋 获取Cookie的步骤

### 方法一：使用Chrome浏览器

1. **登录AugmentCode**
   - 打开浏览器，访问 [https://augmentcode.com](https://augmentcode.com)
   - 使用您的账号正常登录

2. **打开开发者工具**
   - 按 `F12` 键，或右键点击页面选择"检查"
   - 开发者工具面板将在页面底部或右侧打开

3. **切换到Network标签页**
   - 在开发者工具中点击 "Network" 标签页
   - 如果看不到任何请求，请刷新页面 (`F5`)

4. **查找请求**
   - 在请求列表中找到任意一个对 `augmentcode.com` 的请求
   - 点击该请求以查看详细信息

5. **复制Cookie**
   - 在请求详情中找到 "Request Headers" 部分
   - 找到 `Cookie:` 行
   - 复制整个Cookie值（从冒号后面开始的所有内容）

### 方法二：使用Application标签页

1. **登录并打开开发者工具**
   - 登录 [https://augmentcode.com](https://augmentcode.com)
   - 按 `F12` 打开开发者工具

2. **切换到Application标签页**
   - 点击 "Application" 标签页
   - 在左侧面板中展开 "Storage" → "Cookies"
   - 点击 `https://augmentcode.com`

3. **复制Cookie值**
   - 您会看到所有Cookie的列表
   - 手动复制重要的Cookie，格式如：`name1=value1; name2=value2; name3=value3`

## 🔧 在工具中使用Cookie

1. **打开本工具**
   - 启动AugmentCode管理工具
   - 进入"账号管理"页面

2. **输入登录信息**
   - 邮箱地址：输入您的AugmentCode账号邮箱
   - 用户名：输入您的用户名（可选）
   - Cookie字符串：粘贴从浏览器复制的Cookie

3. **保存并验证**
   - 点击"保存登录信息"
   - 系统会自动验证并显示登录状态

## ⚠️ 注意事项

### 安全提醒
- **Cookie包含敏感信息**：请不要将Cookie分享给他人
- **定期更新**：Cookie可能会过期，需要定期重新获取
- **安全存储**：本工具会将Cookie加密存储在本地

### 常见问题

**Q: Cookie多长时间会过期？**
A: 通常Cookie的有效期由网站设定，可能是几小时到几天不等。如果发现无法正常使用，请重新获取Cookie。

**Q: 为什么复制的Cookie无效？**
A: 请确保：
- 您已经成功登录AugmentCode网站
- 复制了完整的Cookie字符串
- Cookie格式正确（包含分号分隔的键值对）

**Q: 如何知道Cookie是否有效？**
A: 保存Cookie后，工具会自动检测登录状态。如果显示"已登录"，说明Cookie有效。

## 🔄 更新Cookie

当Cookie过期时：
1. 重新登录AugmentCode网站
2. 按照上述步骤获取新的Cookie
3. 在工具中点击"编辑登录信息"更新Cookie

## 📞 获取帮助

如果您在获取Cookie过程中遇到问题：
- 确保浏览器版本是最新的
- 尝试清除浏览器缓存后重新登录
- 检查是否有浏览器扩展干扰

---

**免责声明**：本工具仅用于管理您自己的AugmentCode账号。请遵守AugmentCode的服务条款，不要用于任何违法或不当用途。
