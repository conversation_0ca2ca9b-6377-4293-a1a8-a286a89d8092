#!/usr/bin/env python3
"""
仪表盘视图
显示系统状态概览和关键信息
"""

import os
import threading
from datetime import datetime

import customtkinter as ctk
from utils.account_detector import get_all_account_info


class DashboardView:
    """仪表盘视图类"""
    
    def __init__(self, parent_frame):
        """初始化仪表盘视图
        
        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame
        
        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="系统仪表盘",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 创建状态卡片
        self._create_status_cards()
        
        # 加载数据
        threading.Thread(target=self._load_data, daemon=True).start()
    
    def _create_status_cards(self):
        """创建状态卡片"""
        # 创建卡片容器
        cards_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        cards_frame.pack(fill="both", expand=True)
        
        # 分为2列布局
        cards_frame.columnconfigure(0, weight=1)
        cards_frame.columnconfigure(1, weight=1)
        
        # 账号状态卡片
        self.account_card = self._create_card(
            cards_frame, 0, 0, "账号状态", "#4CAF50"
        )
        
        # 系统检测卡片
        self.system_card = self._create_card(
            cards_frame, 0, 1, "系统检测", "#2196F3"
        )
        
        # 工作区状态卡片
        self.workspace_card = self._create_card(
            cards_frame, 1, 0, "工作区状态", "#9C27B0"
        )
        
        # 活动状态卡片
        self.activity_card = self._create_card(
            cards_frame, 1, 1, "活动状态", "#FF9800"
        )
    
    def _create_card(self, parent, row, col, title, color):
        """创建卡片
        
        Args:
            parent: 父容器
            row: 行位置
            col: 列位置
            title: 卡片标题
            color: 卡片颜色
        
        Returns:
            dict: 包含卡片相关组件的字典
        """
        # 卡片容器
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
        
        # 顶部颜色条
        color_bar = ctk.CTkFrame(card_frame, fg_color=color, height=5)
        color_bar.pack(fill="x")
        
        # 卡片标题
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 加载指示器
        loader_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        loader_frame.pack(fill="both", expand=True, padx=15, pady=10)
        
        loader_label = ctk.CTkLabel(
            loader_frame,
            text="加载中...",
            font=ctk.CTkFont(size=14)
        )
        loader_label.pack(pady=30)
        
        # 内容区域
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        
        return {
            "frame": card_frame,
            "loader": loader_frame,
            "content": content_frame,
            "color": color
        }
    
    def _load_data(self):
        """加载数据"""
        try:
            # 获取系统数据
            account_info = get_all_account_info()
            
            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_ui(account_info))
            
        except Exception as e:
            # 显示错误信息
            self.parent_frame.after(0, lambda: self._show_error(str(e)))
    
    def _update_ui(self, data):
        """更新UI
        
        Args:
            data: 加载的数据
        """
        # 更新账号状态卡片
        self._update_account_card(data.get('augment_account', {}))
        
        # 更新系统检测卡片
        self._update_system_card(data.get('augment_account', {}))
        
        # 更新工作区状态卡片
        self._update_workspace_card(data.get('workspace_info', {}))
        
        # 更新活动状态卡片
        self._update_activity_card(data.get('augment_activity', {}))
    
    def _update_account_card(self, account_data):
        """更新账号状态卡片
        
        Args:
            account_data: 账号数据
        """
        # 隐藏加载器
        self.account_card["loader"].pack_forget()
        
        # 显示内容区域
        self.account_card["content"].pack(fill="both", expand=True, padx=15, pady=10)
        
        # 根据登录状态显示不同内容
        if account_data.get('logged_in', False):
            status_label = ctk.CTkLabel(
                self.account_card["content"],
                text="✅ 已登录",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.account_card["color"]
            )
            status_label.pack(pady=(0, 10))
            
            # 邮箱信息
            if account_data.get('email'):
                email_frame = ctk.CTkFrame(self.account_card["content"], fg_color="transparent")
                email_frame.pack(fill="x")
                
                email_label = ctk.CTkLabel(
                    email_frame,
                    text="邮箱:",
                    font=ctk.CTkFont(size=12, weight="bold"),
                    width=60
                )
                email_label.pack(side="left", padx=5, pady=2)
                
                email_value = ctk.CTkLabel(
                    email_frame,
                    text=account_data.get('email'),
                    font=ctk.CTkFont(size=12)
                )
                email_value.pack(side="left", fill="x", padx=5, pady=2)
            
            # 用户名信息
            if account_data.get('username'):
                username_frame = ctk.CTkFrame(self.account_card["content"], fg_color="transparent")
                username_frame.pack(fill="x")
                
                username_label = ctk.CTkLabel(
                    username_frame,
                    text="用户名:",
                    font=ctk.CTkFont(size=12, weight="bold"),
                    width=60
                )
                username_label.pack(side="left", padx=5, pady=2)
                
                username_value = ctk.CTkLabel(
                    username_frame,
                    text=account_data.get('username'),
                    font=ctk.CTkFont(size=12)
                )
                username_value.pack(side="left", fill="x", padx=5, pady=2)
        else:
            status_label = ctk.CTkLabel(
                self.account_card["content"],
                text="❌ 未登录",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="gray"
            )
            status_label.pack(pady=(0, 10))
            
            hint_label = ctk.CTkLabel(
                self.account_card["content"],
                text="没有检测到登录信息",
                font=ctk.CTkFont(size=12),
                text_color="gray"
            )
            hint_label.pack(pady=5)
    
    def _update_system_card(self, account_data):
        """更新系统检测卡片
        
        Args:
            account_data: 账号数据
        """
        # 隐藏加载器
        self.system_card["loader"].pack_forget()
        
        # 显示内容区域
        self.system_card["content"].pack(fill="both", expand=True, padx=15, pady=10)
        
        # 根据检测状态显示不同内容
        if account_data.get('logged_in', False):
            status_label = ctk.CTkLabel(
                self.system_card["content"],
                text="✅ 检测到登录",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.system_card["color"]
            )
            status_label.pack(pady=(0, 10))
            
            # 登录方式
            method_frame = ctk.CTkFrame(self.system_card["content"], fg_color="transparent")
            method_frame.pack(fill="x")
            
            method_label = ctk.CTkLabel(
                method_frame,
                text="登录方式:",
                font=ctk.CTkFont(size=12, weight="bold"),
                width=80
            )
            method_label.pack(side="left", padx=5, pady=2)
            
            method_value = ctk.CTkLabel(
                method_frame,
                text=account_data.get('login_method', '未知'),
                font=ctk.CTkFont(size=12)
            )
            method_value.pack(side="left", fill="x", padx=5, pady=2)
            
            # Cookie状态
            cookie_frame = ctk.CTkFrame(self.system_card["content"], fg_color="transparent")
            cookie_frame.pack(fill="x")
            
            cookie_label = ctk.CTkLabel(
                cookie_frame,
                text="Cookie:",
                font=ctk.CTkFont(size=12, weight="bold"),
                width=80
            )
            cookie_label.pack(side="left", padx=5, pady=2)
            
            cookie_info = account_data.get('cookie_info', {})
            cookie_status = "已检测到" if cookie_info.get('has_cookie', False) else "未检测到"
            
            cookie_value = ctk.CTkLabel(
                cookie_frame,
                text=cookie_status,
                font=ctk.CTkFont(size=12)
            )
            cookie_value.pack(side="left", fill="x", padx=5, pady=2)
        else:
            status_label = ctk.CTkLabel(
                self.system_card["content"],
                text="❌ 未检测到登录",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="gray"
            )
            status_label.pack(pady=(0, 10))
            
            hint_label = ctk.CTkLabel(
                self.system_card["content"],
                text="系统中未检测到AugmentCode登录配置",
                font=ctk.CTkFont(size=12),
                text_color="gray"
            )
            hint_label.pack(pady=5)
    
    def _update_workspace_card(self, workspace_data):
        """更新工作区状态卡片
        
        Args:
            workspace_data: 工作区数据
        """
        # 隐藏加载器
        self.workspace_card["loader"].pack_forget()
        
        # 显示内容区域
        self.workspace_card["content"].pack(fill="both", expand=True, padx=15, pady=10)
        
        # VS Code 配置检测
        vscode_path = os.path.expanduser("~/.vscode")
        vscode_exists = os.path.exists(vscode_path)
        
        if vscode_exists:
            status_label = ctk.CTkLabel(
                self.workspace_card["content"],
                text="📊 工作区状态",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.workspace_card["color"]
            )
            status_label.pack(pady=(0, 10))
            
            # VS Code 配置
            config_frame = ctk.CTkFrame(self.workspace_card["content"], fg_color="transparent")
            config_frame.pack(fill="x")
            
            config_label = ctk.CTkLabel(
                config_frame,
                text="VS Code:",
                font=ctk.CTkFont(size=12, weight="bold"),
                width=80
            )
            config_label.pack(side="left", padx=5, pady=2)
            
            config_value = ctk.CTkLabel(
                config_frame,
                text="已检测到",
                font=ctk.CTkFont(size=12)
            )
            config_value.pack(side="left", fill="x", padx=5, pady=2)
            
            # 扩展目录
            ext_dir = os.path.join(vscode_path, "extensions")
            ext_frame = ctk.CTkFrame(self.workspace_card["content"], fg_color="transparent")
            ext_frame.pack(fill="x")
            
            ext_label = ctk.CTkLabel(
                ext_frame,
                text="扩展目录:",
                font=ctk.CTkFont(size=12, weight="bold"),
                width=80
            )
            ext_label.pack(side="left", padx=5, pady=2)
            
            ext_value = ctk.CTkLabel(
                ext_frame,
                text="已检测到" if os.path.exists(ext_dir) else "未检测到",
                font=ctk.CTkFont(size=12)
            )
            ext_value.pack(side="left", fill="x", padx=5, pady=2)
        else:
            status_label = ctk.CTkLabel(
                self.workspace_card["content"],
                text="❓ 未检测到 VS Code",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="gray"
            )
            status_label.pack(pady=(0, 10))
            
            hint_label = ctk.CTkLabel(
                self.workspace_card["content"],
                text="未找到 VS Code 配置目录",
                font=ctk.CTkFont(size=12),
                text_color="gray"
            )
            hint_label.pack(pady=5)
    
    def _update_activity_card(self, activity_data):
        """更新活动状态卡片
        
        Args:
            activity_data: 活动数据
        """
        # 隐藏加载器
        self.activity_card["loader"].pack_forget()
        
        # 显示内容区域
        self.activity_card["content"].pack(fill="both", expand=True, padx=15, pady=10)
        
        # 根据活动状态显示不同内容
        is_active = activity_data.get('is_active', False)
        
        if is_active:
            status_label = ctk.CTkLabel(
                self.activity_card["content"],
                text="🚀 检测到活动",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.activity_card["color"]
            )
            status_label.pack(pady=(0, 10))
            
            # 根据检测到的活动创建信息行
            if activity_data.get('extension_confirmed'):
                self._create_info_row(self.activity_card["content"], "扩展确认", "已确认")
            
            if activity_data.get('publisher_trusted'):
                self._create_info_row(self.activity_card["content"], "发布者信任", "已信任")
            
            if activity_data.get('chat_history'):
                self._create_info_row(self.activity_card["content"], "聊天历史", "有记录")
            
            webview_sessions = activity_data.get('webview_sessions', [])
            if webview_sessions:
                self._create_info_row(self.activity_card["content"], "会话", f"{len(webview_sessions)}个")
            
        else:
            status_label = ctk.CTkLabel(
                self.activity_card["content"],
                text="😴 未检测到活动",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="gray"
            )
            status_label.pack(pady=(0, 10))
            
            hint_label = ctk.CTkLabel(
                self.activity_card["content"],
                text="未发现明显的AugmentCode活动痕迹",
                font=ctk.CTkFont(size=12),
                text_color="gray"
            )
            hint_label.pack(pady=5)
    
    def _create_info_row(self, parent, label, value):
        """创建信息行
        
        Args:
            parent: 父容器
            label: 标签
            value: 值
        """
        frame = ctk.CTkFrame(parent, fg_color="transparent")
        frame.pack(fill="x")
        
        label_widget = ctk.CTkLabel(
            frame,
            text=f"{label}:",
            font=ctk.CTkFont(size=12, weight="bold"),
            width=80
        )
        label_widget.pack(side="left", padx=5, pady=2)
        
        value_widget = ctk.CTkLabel(
            frame,
            text=value,
            font=ctk.CTkFont(size=12)
        )
        value_widget.pack(side="left", fill="x", padx=5, pady=2)
    
    def _show_error(self, error_message):
        """显示错误信息
        
        Args:
            error_message: 错误信息
        """
        # 显示错误在所有卡片上
        for card in [self.account_card, self.system_card, self.workspace_card, self.activity_card]:
            # 隐藏加载器
            card["loader"].pack_forget()
            
            # 显示内容区域
            card["content"].pack(fill="both", expand=True, padx=15, pady=10)
            
            # 错误信息
            error_label = ctk.CTkLabel(
                card["content"],
                text="❌ 加载失败",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="#F44336"
            )
            error_label.pack(pady=(0, 5))
            
            # 在第一个卡片中显示详细错误信息
            if card == self.account_card:
                error_detail = ctk.CTkLabel(
                    card["content"],
                    text=f"错误: {error_message}",
                    font=ctk.CTkFont(size=12),
                    text_color="gray",
                    wraplength=200
                )
                error_detail.pack(pady=5)