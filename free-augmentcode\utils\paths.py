#!/usr/bin/env python3
"""
路径工具
提供获取各种系统路径的函数
"""

import os
import platform


def get_home_dir():
    """获取用户主目录路径
    
    Returns:
        str: 用户主目录路径
    """
    return os.path.expanduser("~")


def get_app_data_dir():
    """获取应用数据目录路径
    
    Returns:
        str: 应用数据目录路径
    """
    if platform.system() == "Windows":
        return os.path.expandvars("%APPDATA%")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Library/Application Support")
    else:  # Linux
        return os.path.expanduser("~/.config")


def get_vscode_path():
    """获取VS Code配置路径
    
    Returns:
        str: VS Code配置路径
    """
    if platform.system() == "Windows":
        return os.path.expandvars("%APPDATA%/Code")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Library/Application Support/Code")
    else:  # Linux
        return os.path.expanduser("~/.config/Code")


def get_storage_path():
    """获取VS Code存储路径
    
    Returns:
        str: VS Code存储路径
    """
    vscode_path = get_vscode_path()
    return os.path.join(vscode_path, "User", "globalStorage")


def get_workspace_storage_path():
    """获取VS Code工作区存储路径
    
    Returns:
        str: VS Code工作区存储路径
    """
    vscode_path = get_vscode_path()
    return os.path.join(vscode_path, "User", "workspaceStorage")


def get_db_path():
    """获取VS Code数据库路径
    
    Returns:
        str: VS Code数据库路径
    """
    vscode_path = get_vscode_path()
    return os.path.join(vscode_path, "User", "globalState.json")


def get_machine_id_path():
    """获取机器ID文件路径
    
    Returns:
        str: 机器ID文件路径
    """
    vscode_path = get_vscode_path()
    return os.path.join(vscode_path, "machineid")


def get_extensions_path():
    """获取VS Code扩展路径
    
    Returns:
        str: VS Code扩展路径
    """
    if platform.system() == "Windows":
        return os.path.expandvars("%USERPROFILE%/.vscode/extensions")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/.vscode/extensions")
    else:  # Linux
        return os.path.expanduser("~/.vscode/extensions")


def get_auth_db_path():
    """获取VS Code认证数据库路径
    
    Returns:
        str: VS Code认证数据库路径
    """
    if platform.system() == "Windows":
        app_path = os.path.expandvars("%APPDATA%/Code/User/workspaceStorage")
    elif platform.system() == "Darwin":
        app_path = os.path.expanduser("~/Library/Application Support/Code/User/workspaceStorage")
    else:
        app_path = os.path.expanduser("~/.config/Code/User/workspaceStorage")
    
    return app_path