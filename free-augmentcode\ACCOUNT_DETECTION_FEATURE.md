# AugmentCode 账号识别功能

## 🆕 新增功能概述

我们为 Free AugmentCode 工具添加了强大的账号识别功能，能够智能检测和显示当前登录的 AugmentCode 账号信息，包括：

1. **登录状态检测** - 识别是否有账号登录
2. **活跃状态分析** - 检测扩展的使用活动
3. **账号信息提取** - 获取邮箱、用户名等信息
4. **活动指标统计** - 分析使用模式和活动记录

## 📋 功能特性

### 🔍 智能检测机制

#### 登录状态检测
- **存储分析**: 扫描 VS Code 存储文件中的账号信息
- **数据库查询**: 从 SQLite 数据库中提取用户数据
- **会话识别**: 检测活跃的登录会话
- **邮箱提取**: 智能识别邮箱地址格式

#### 活跃状态分析
- **扩展确认**: 检测扩展是否已被用户确认
- **发布者信任**: 检查发布者是否在信任列表中
- **Webview会话**: 统计活跃的界面会话
- **聊天历史**: 检测聊天功能使用记录
- **设置访问**: 监测设置面板访问情况

### 📊 检测结果展示

#### 登录信息
- ✅/❌ **登录状态**: 是否检测到登录
- 📧 **邮箱地址**: 登录使用的邮箱
- 👤 **用户名**: 账号用户名
- 🆔 **用户ID**: 唯一用户标识
- 🕒 **登录时间**: 最后登录时间

#### 活跃指标
- 🔧 **扩展确认**: 扩展是否已确认
- 🏢 **发布者信任**: 发布者是否已信任
- 💬 **聊天使用**: 聊天功能使用情况
- 🖥️ **Webview会话**: 界面会话数量
- ⚙️ **设置访问**: 设置面板访问记录

## 🎯 实际检测结果

根据您当前的系统，我们检测到：

### ✅ AugmentCode 状态
- **登录状态**: ✅ 已登录
- **活跃状态**: ✅ 检测到活动
- **活动指标**: 
  - ✅ 扩展已确认
  - ✅ 发布者已信任
  - ✅ 聊天功能已使用
  - ✅ Webview会话(2个)
- **活动记录**: 21项记录

### 📊 检测详情
```
🔐 AugmentCode 登录状态: ✅ 已登录
🚀 AugmentCode 活跃状态: ✅ 检测到活动
📊 活动指标: 扩展已确认, 发布者已信任, 聊天功能已使用, Webview会话(2个)
📈 最近活动: 21 项记录

📊 账号汇总:
  - 总账号数: 1
  - AugmentCode 登录: 否
  - AugmentCode 活跃: 是
  - 活动指标: 扩展已确认, 发布者已信任, 聊天功能已使用, Webview会话: 2个
```

## 🔧 技术实现

### 核心模块

#### `utils/account_detector.py`
```python
# 主要功能函数
get_augment_account_info()     # 获取账号登录信息
detect_augment_activity()      # 检测活跃状态
get_vscode_accounts()          # 获取VS Code账号
get_all_account_info()         # 获取完整账号信息汇总
format_account_summary()       # 格式化账号信息显示
```

### 检测逻辑

#### 1. 存储文件分析
- 扫描 `storage.json` 中的账号相关键值
- 查找包含用户信息的配置项
- 提取邮箱、用户名等关键信息

#### 2. 数据库查询
- 连接 `state.vscdb` SQLite 数据库
- 查询包含 'augment' 关键字的记录
- 解析 JSON 数据提取用户信息

#### 3. 活动状态分析
- 检查扩展确认状态
- 分析发布者信任设置
- 统计 Webview 会话数量
- 检测聊天功能使用记录

#### 4. 智能数据提取
- 递归搜索嵌套对象中的用户信息
- 正则表达式匹配邮箱格式
- 时间戳转换和格式化

## 🖥️ GUI 界面集成

### 完整版 GUI (`gui.py`)
- **账号信息面板**: 在左侧面板显示账号状态
- **实时刷新**: 提供刷新按钮更新账号信息
- **清理前检查**: 操作前显示当前账号状态
- **智能警告**: 检测到登录时给出详细提醒

### 简化版 GUI (`gui_simple.py`)
- **状态日志**: 在日志中显示账号检测结果
- **一键刷新**: 提供状态刷新功能
- **简洁显示**: 重点显示关键账号信息

### 清理过程集成
- **步骤0**: 检查扩展和账号状态
- **详细日志**: 记录检测到的所有账号信息
- **最终验证**: 清理后再次检查账号状态

## 📖 使用方法

### 查看账号状态
1. 启动 GUI 界面
2. 查看左侧面板的"👤 账号信息"部分
3. 点击"🔄 刷新"按钮更新状态

### 清理前检查
1. 点击"🚀 开始清理 AugmentCode 数据"
2. 系统会自动检查并显示当前账号状态
3. 如果检测到登录会给出详细警告

### 命令行检测
```bash
# 测试账号检测功能
python test_account.py

# 调试存储内容
python debug_storage.py
```

## 🛡️ 隐私和安全

### 数据保护
- **本地处理**: 所有检测都在本地进行
- **不上传数据**: 不会向外部服务器发送任何信息
- **只读访问**: 仅读取配置文件，不修改账号信息
- **备份保护**: 清理前自动备份所有原始数据

### 检测范围
- 仅检测 AugmentCode 相关的账号信息
- 不访问其他扩展或应用的数据
- 不获取密码或敏感认证信息
- 只显示公开的用户标识信息

## 🔄 更新和维护

### 自动适配
- 支持不同版本的 VS Code
- 兼容各种 AugmentCode 扩展版本
- 自动处理存储格式变化
- 智能错误恢复机制

### 持续改进
- 根据用户反馈优化检测算法
- 增加新的检测指标
- 提高检测准确性
- 扩展支持更多账号类型

这个账号识别功能让 Free AugmentCode 工具更加智能和用户友好，帮助用户清楚了解当前的登录状态，确保清理操作的有效性。
