#!/usr/bin/env python3
"""
测试订阅信息解析功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from utils.subscription_checker import SubscriptionChecker
from utils.account_manager import get_current_login_info, get_account_subscription_info

def test_html_parsing():
    """测试HTML解析功能"""
    print("🧪 测试HTML解析功能")
    print("=" * 50)
    
    # 模拟您的AugmentCode订阅页面HTML内容
    sample_html = """
    <html>
    <body>
        <div class="subscription-page">
            <h1>Subscription</h1>
            <p>Manage your subscription and billing details.</p>

            <div class="user-messages">
                <h2>User Messages</h2>
                <div class="available-count">27.00 available</div>
                <div class="renew-info">50 renew monthly</div>
                <div class="usage-info">Used 23 of 50 this month</div>
            </div>

            <div class="billing">
                <h2>Billing</h2>
                <div class="next-billing">July 11, 2025</div>
                <div class="billing-label">Next Billing Date</div>
                <!-- 更真实的HTML结构 -->
                <div>July 11, 2025</div>
            </div>

            <div class="current-plan">
                <h2>Current plan</h2>
                <div class="plan-name">Community Plan</div>
                <ul>
                    <li>50 user messages per month</li>
                    <li>Context Engine</li>
                    <li>MCP & Native Tools</li>
                </ul>
                <div class="plan-cost">$0.00/user/mo • 1 seat purchased</div>
                <div class="monthly-total">Monthly total: $0.00</div>
            </div>
        </div>
    </body>
    </html>
    """
    
    print("📄 测试HTML内容解析...")
    
    checker = SubscriptionChecker()
    result = checker._parse_html_response(sample_html)
    
    print("\n📊 解析结果:")
    print("-" * 30)
    print(f"📋 计划名称: {result['plan_name']}")
    print(f"🔢 已使用: {result['usage_count']} 次")
    print(f"📈 使用限制: {result['usage_limit']} 次")
    print(f"⚡ 剩余次数: {result['remaining_count']} 次")
    print(f"🔄 重置日期: {result['reset_date']}")
    print(f"✅ 状态: {result['subscription_status']}")
    
    if result.get('parse_error'):
        print(f"⚠️ 解析错误: {result['parse_error']}")
    
    # 验证解析结果
    expected_values = {
        'plan_name': 'Community Plan',
        'usage_count': 23,
        'usage_limit': 50,
        'remaining_count': 27,
        'reset_date': 'July 11, 2025'
    }
    
    print("\n🎯 验证结果:")
    print("-" * 30)
    
    all_correct = True
    for key, expected in expected_values.items():
        actual = result.get(key)
        if str(actual).strip() == str(expected).strip():
            print(f"✅ {key}: {actual} (正确)")
        else:
            print(f"❌ {key}: 期望 '{expected}', 实际 '{actual}'")
            all_correct = False
    
    if all_correct:
        print("\n🎉 所有解析结果正确！")
    else:
        print("\n⚠️ 部分解析结果需要调整")
    
    return result

def test_real_subscription_check():
    """测试真实的订阅信息检查"""
    print("\n" + "=" * 50)
    print("🔍 测试真实订阅信息检查")
    print("=" * 50)
    
    # 获取当前登录信息
    current_info = get_current_login_info()
    if not current_info:
        print("❌ 未找到登录信息，无法测试真实订阅检查")
        return
    
    print(f"👤 当前登录账号: {current_info['email']}")
    
    # 测试订阅信息获取
    success, sub_info = get_account_subscription_info()
    
    print(f"\n📊 订阅信息获取结果: {'成功' if success else '失败'}")
    print("-" * 30)
    
    if success:
        print(f"📋 计划名称: {sub_info.get('plan_name', '未知')}")
        print(f"🔢 已使用: {sub_info.get('usage_count', 0)} 次")
        print(f"📈 使用限制: {sub_info.get('usage_limit', 0)} 次")
        print(f"⚡ 剩余次数: {sub_info.get('remaining_count', 0)} 次")
        print(f"🔄 重置日期: {sub_info.get('reset_date', '未知')}")
        print(f"✅ 状态: {sub_info.get('subscription_status', '未知')}")
        
        # 计算使用百分比
        usage_count = sub_info.get('usage_count', 0)
        usage_limit = sub_info.get('usage_limit', 0)
        if usage_limit > 0:
            usage_percent = (usage_count / usage_limit) * 100
            print(f"📊 使用百分比: {usage_percent:.1f}%")
            
            # 状态评估
            remaining = sub_info.get('remaining_count', 0)
            if remaining > 50:
                status_color = "🟢"
                status_text = "充足"
            elif remaining > 10:
                status_color = "🟡"
                status_text = "注意"
            else:
                status_color = "🔴"
                status_text = "紧急"
            
            print(f"🎯 状态评估: {status_color} {status_text}")
    else:
        error_msg = sub_info.get('error', '未知错误')
        print(f"❌ 获取失败: {error_msg}")
        
        # 如果是Cookie问题，提供建议
        if 'cookie' in error_msg.lower():
            print("\n💡 建议:")
            print("1. 检查Cookie是否有效")
            print("2. 尝试重新登录")
            print("3. 确认网络连接正常")

def main():
    """主函数"""
    print("🎯 订阅信息解析测试")
    print("=" * 60)
    
    try:
        # 测试HTML解析
        test_html_parsing()
        
        # 测试真实订阅检查
        test_real_subscription_check()
        
        print("\n" + "=" * 60)
        print("🎯 测试完成！")
        
        print("\n💡 更新说明:")
        print("• 增强了HTML解析能力，支持AugmentCode的订阅页面格式")
        print("• 能够识别 '27.00 available' 格式的剩余次数")
        print("• 能够解析 'Used 23 of 50 this month' 格式的使用情况")
        print("• 能够识别 'Community Plan' 等计划名称")
        print("• 能够解析 'July 11, 2025' 格式的重置日期")
        print("• 提供了多种备用解析模式以提高兼容性")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
