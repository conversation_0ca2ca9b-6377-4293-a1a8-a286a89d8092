#!/usr/bin/env python3
"""
测试增强的账号信息获取方法
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

def test_enhanced_methods():
    """测试增强方法"""
    print("🧪 测试增强的账号信息获取方法")
    print("=" * 60)
    
    # 检查依赖
    print("🔍 检查依赖...")
    missing_deps = []
    
    try:
        import requests
        print("✅ requests")
    except ImportError:
        missing_deps.append("requests")
        print("❌ requests")
    
    try:
        import selenium
        print("✅ selenium")
    except ImportError:
        missing_deps.append("selenium")
        print("❌ selenium")
    
    try:
        from bs4 import BeautifulSoup
        print("✅ beautifulsoup4")
    except ImportError:
        missing_deps.append("beautifulsoup4")
        print("❌ beautifulsoup4")
    
    if missing_deps:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing_deps)}")
        print("💡 请运行: python install_enhanced_dependencies.py")
        return
    
    # 获取当前登录信息
    try:
        from utils.account_manager import get_current_login_info
        current_info = get_current_login_info()
        
        if not current_info:
            print("\n❌ 未找到登录信息")
            print("💡 请先登录AugmentCode账号")
            return
        
        email = current_info['email']
        cookie_string = current_info.get('cookie_string', '')
        
        if not cookie_string:
            print("\n❌ 未找到Cookie信息")
            return
        
        print(f"\n👤 测试账号: {email}")
        print(f"🍪 Cookie长度: {len(cookie_string)} 字符")
        
    except Exception as e:
        print(f"\n❌ 获取登录信息失败: {e}")
        return
    
    # 测试增强方法
    try:
        from enhanced_account_methods import EnhancedAccountInfoGetter
        
        print(f"\n🚀 开始测试增强方法")
        print("-" * 40)
        
        getter = EnhancedAccountInfoGetter()
        
        # 测试各个方法
        methods = [
            ("直接API调用", getter.method_1_direct_api),
            ("页面抓取", getter.method_2_page_scraping),
            ("网络流量分析", getter.method_4_network_analysis),
        ]
        
        results = {}
        
        for method_name, method_func in methods:
            print(f"\n🔄 测试: {method_name}")
            print("-" * 20)
            
            try:
                success, data = method_func(cookie_string)
                results[method_name] = (success, data)
                
                if success:
                    print(f"✅ {method_name} 成功")
                    print(f"📊 数据预览:")
                    for key, value in data.items():
                        if key != 'error' and value:
                            print(f"  {key}: {value}")
                else:
                    print(f"❌ {method_name} 失败: {data.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ {method_name} 异常: {e}")
                results[method_name] = (False, {'error': str(e)})
        
        # 测试综合方法
        print(f"\n🎯 测试综合方法")
        print("-" * 20)
        
        try:
            success, data = getter.get_account_info_comprehensive(cookie_string)
            
            if success:
                print("✅ 综合方法成功")
                print(f"\n📊 最终获取的数据:")
                print("-" * 30)
                print(f"📋 计划名称: {data.get('plan_name', '未知')}")
                print(f"🔢 已使用: {data.get('usage_count', 0)} 次")
                print(f"📈 使用限制: {data.get('usage_limit', 0)} 次")
                print(f"⚡ 剩余次数: {data.get('remaining_count', 0)} 次")
                print(f"🔄 重置日期: {data.get('reset_date', '未知')}")
                print(f"✅ 状态: {data.get('subscription_status', '未知')}")
                
                # 状态评估
                remaining = data.get('remaining_count', 0)
                if remaining > 50:
                    status_color = "🟢"
                    status_text = "充足"
                elif remaining > 10:
                    status_color = "🟡"
                    status_text = "注意"
                else:
                    status_color = "🔴"
                    status_text = "紧急"
                
                print(f"🎯 状态评估: {status_color} {status_text}")
                
            else:
                print(f"❌ 综合方法失败: {data.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 综合方法异常: {e}")
        
        # 结果总结
        print(f"\n📈 测试结果总结")
        print("=" * 40)
        
        successful_methods = []
        failed_methods = []
        
        for method_name, (success, data) in results.items():
            if success:
                successful_methods.append(method_name)
            else:
                failed_methods.append(method_name)
        
        print(f"✅ 成功方法 ({len(successful_methods)}): {', '.join(successful_methods) if successful_methods else '无'}")
        print(f"❌ 失败方法 ({len(failed_methods)}): {', '.join(failed_methods) if failed_methods else '无'}")
        
        if successful_methods:
            print(f"\n🎉 至少有 {len(successful_methods)} 种方法可以获取账号信息！")
        else:
            print(f"\n😞 所有方法都失败了，可能需要:")
            print("• 检查Cookie是否有效")
            print("• 确认网络连接正常")
            print("• 验证AugmentCode网站是否可访问")
            print("• 尝试重新登录")
        
    except ImportError:
        print("\n❌ 增强方法模块未找到")
        print("💡 请确保 enhanced_account_methods.py 文件存在")
    except Exception as e:
        print(f"\n❌ 测试增强方法失败: {e}")
        import traceback
        traceback.print_exc()

def test_integration_with_existing():
    """测试与现有系统的集成"""
    print(f"\n🔗 测试与现有系统的集成")
    print("=" * 40)
    
    try:
        from utils.account_manager import get_account_subscription_info
        
        print("🔄 调用集成后的订阅信息获取...")
        success, sub_info = get_account_subscription_info()
        
        if success:
            print("✅ 集成成功")
            print(f"📊 获取的数据:")
            for key, value in sub_info.items():
                if key != 'error' and value:
                    print(f"  {key}: {value}")
        else:
            print(f"❌ 集成失败: {sub_info.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")

def main():
    """主函数"""
    print("🎯 增强账号信息获取方法测试")
    print("=" * 60)
    
    try:
        # 测试增强方法
        test_enhanced_methods()
        
        # 测试集成
        test_integration_with_existing()
        
        print("\n" + "=" * 60)
        print("🎯 测试完成！")
        
        print("\n💡 开源方法总结:")
        print("• 直接API调用: 尝试各种可能的API端点")
        print("• 页面抓取: 使用BeautifulSoup和正则表达式解析")
        print("• 浏览器自动化: 使用Selenium模拟真实浏览器")
        print("• 网络流量分析: 分析页面中的隐藏API端点")
        print("• 反检测技术: 随机User-Agent、请求延迟等")
        
        print("\n🔧 技术栈:")
        print("• requests: HTTP请求")
        print("• selenium: 浏览器自动化")
        print("• beautifulsoup4: HTML解析")
        print("• 正则表达式: 模式匹配")
        print("• 会话管理: Cookie和状态保持")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
