#!/usr/bin/env python3
"""
账号状态页面的额外方法
"""

import customtkinter as ctk
from tkinter import messagebox
from utils.account_manager import (
    get_current_login_info, get_account_subscription_info, get_account_manager
)
from utils.cookie_login import quick_validate_login
from utils.account_detector import get_all_account_info


def add_system_detection_methods(self):
    """为账号状态页面添加系统检测方法"""
    
    def _refresh_system_detection(self):
        """刷新系统检测信息"""
        # 清空现有内容
        for widget in self.detection_content_frame.winfo_children():
            widget.destroy()
        
        try:
            # 获取系统检测的账号信息
            account_info = get_all_account_info()
            augment_account = account_info['augment_account']
            
            # 系统检测状态
            if augment_account['logged_in']:
                status_label = ctk.CTkLabel(
                    self.detection_content_frame,
                    text="✅ 系统检测到AugmentCode登录",
                    text_color="green",
                    font=ctk.CTkFont(size=14, weight="bold")
                )
                status_label.pack(pady=(10, 5))
                
                # 系统检测的详细信息
                detection_items = []
                
                if augment_account['email']:
                    detection_items.append(("📧 检测邮箱", augment_account['email']))
                
                if augment_account['username']:
                    detection_items.append(("👤 检测用户名", augment_account['username']))
                
                if augment_account['login_time']:
                    detection_items.append(("🕒 检测登录时间", augment_account['login_time']))
                
                login_method = augment_account.get('login_method', 'unknown')
                detection_items.append(("🔐 登录方式", login_method))
                
                # Cookie信息
                cookie_info = augment_account.get('cookie_info', {})
                if cookie_info.get('has_cookie'):
                    detection_items.append(("🍪 Cookie状态", f"已检测到 ({cookie_info['cookie_length']} 字符)"))
                else:
                    detection_items.append(("🍪 Cookie状态", "未检测到"))
                
                # 显示检测项目
                for label, value in detection_items:
                    item_frame = ctk.CTkFrame(self.detection_content_frame)
                    item_frame.pack(fill="x", padx=10, pady=2)
                    
                    label_widget = ctk.CTkLabel(
                        item_frame,
                        text=f"{label}:",
                        font=ctk.CTkFont(size=12, weight="bold"),
                        width=120
                    )
                    label_widget.pack(side="left", padx=(10, 5), pady=5)
                    
                    value_widget = ctk.CTkLabel(
                        item_frame,
                        text=str(value),
                        font=ctk.CTkFont(size=12)
                    )
                    value_widget.pack(side="left", padx=5, pady=5)
                
            else:
                status_label = ctk.CTkLabel(
                    self.detection_content_frame,
                    text="❌ 系统未检测到AugmentCode登录",
                    text_color="red",
                    font=ctk.CTkFont(size=14, weight="bold")
                )
                status_label.pack(pady=(10, 5))
                
                hint_label = ctk.CTkLabel(
                    self.detection_content_frame,
                    text="系统在VS Code配置中未找到AugmentCode登录信息",
                    text_color="gray",
                    font=ctk.CTkFont(size=11)
                )
                hint_label.pack(pady=(0, 10))
                
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.detection_content_frame,
                text=f"❌ 系统检测失败: {str(e)}",
                text_color="red",
                font=ctk.CTkFont(size=12)
            )
            error_label.pack(pady=10)
    
    def _refresh_activity_status(self):
        """刷新活动状态信息"""
        # 清空现有内容
        for widget in self.activity_content_frame.winfo_children():
            widget.destroy()
        
        try:
            # 获取活动检测信息
            account_info = get_all_account_info()
            augment_activity = account_info['augment_activity']
            
            if augment_activity['is_active']:
                status_label = ctk.CTkLabel(
                    self.activity_content_frame,
                    text="🚀 检测到AugmentCode活动",
                    text_color="orange",
                    font=ctk.CTkFont(size=14, weight="bold")
                )
                status_label.pack(pady=(10, 5))
                
                # 活动指标
                activity_indicators = []
                
                if augment_activity.get('extension_confirmed'):
                    activity_indicators.append(("✅ 扩展确认", "已确认"))
                
                if augment_activity.get('publisher_trusted'):
                    activity_indicators.append(("🔒 发布者信任", "已信任"))
                
                if augment_activity.get('chat_history'):
                    activity_indicators.append(("💬 聊天历史", "有使用记录"))
                
                if augment_activity.get('webview_sessions'):
                    session_count = len(augment_activity['webview_sessions'])
                    activity_indicators.append(("🌐 Webview会话", f"{session_count} 个会话"))
                
                recent_activity = augment_activity.get('recent_activity', [])
                if recent_activity:
                    activity_indicators.append(("📊 最近活动", f"{len(recent_activity)} 项记录"))
                
                # 显示活动指标
                for label, value in activity_indicators:
                    item_frame = ctk.CTkFrame(self.activity_content_frame)
                    item_frame.pack(fill="x", padx=10, pady=2)
                    
                    label_widget = ctk.CTkLabel(
                        item_frame,
                        text=f"{label}:",
                        font=ctk.CTkFont(size=12, weight="bold"),
                        width=120
                    )
                    label_widget.pack(side="left", padx=(10, 5), pady=5)
                    
                    value_widget = ctk.CTkLabel(
                        item_frame,
                        text=str(value),
                        font=ctk.CTkFont(size=12),
                        text_color="orange"
                    )
                    value_widget.pack(side="left", padx=5, pady=5)
                
                # 活动建议
                suggestion_label = ctk.CTkLabel(
                    self.activity_content_frame,
                    text="💡 建议：检测到活动状态，清理前请完全退出VS Code",
                    text_color="orange",
                    font=ctk.CTkFont(size=11)
                )
                suggestion_label.pack(pady=(5, 10))
                
            else:
                status_label = ctk.CTkLabel(
                    self.activity_content_frame,
                    text="😴 未检测到明显的AugmentCode活动",
                    text_color="green",
                    font=ctk.CTkFont(size=14, weight="bold")
                )
                status_label.pack(pady=(10, 5))
                
                hint_label = ctk.CTkLabel(
                    self.activity_content_frame,
                    text="系统中没有检测到明显的AugmentCode使用痕迹",
                    text_color="gray",
                    font=ctk.CTkFont(size=11)
                )
                hint_label.pack(pady=(0, 10))
                
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.activity_content_frame,
                text=f"❌ 活动状态检测失败: {str(e)}",
                text_color="red",
                font=ctk.CTkFont(size=12)
            )
            error_label.pack(pady=10)
    
    # 将方法绑定到实例
    self._refresh_system_detection = _refresh_system_detection.__get__(self, self.__class__)
    self._refresh_activity_status = _refresh_activity_status.__get__(self, self.__class__)


def add_action_methods(self):
    """为账号状态页面添加操作方法"""
    
    def show_login_dialog(self):
        """显示登录对话框"""
        # 创建登录窗口
        login_window = ctk.CTkToplevel()
        login_window.title("AugmentCode 账号登录")
        login_window.geometry("450x600")
        login_window.resizable(False, False)
        
        # 设置窗口居中
        login_window.transient(self.main_container)
        login_window.grab_set()
        
        # 创建主框架
        main_frame = ctk.CTkFrame(login_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="🔐 AugmentCode 账号登录",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # 邮箱输入
        email_label = ctk.CTkLabel(main_frame, text="📧 邮箱地址:", font=ctk.CTkFont(size=12))
        email_label.pack(pady=(0, 5), anchor="w", padx=10)
        
        email_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="请输入您的邮箱地址",
            width=350,
            height=35
        )
        email_entry.pack(pady=(0, 15), padx=10)
        
        # Cookie输入
        cookie_label = ctk.CTkLabel(main_frame, text="🍪 Cookie信息:", font=ctk.CTkFont(size=12))
        cookie_label.pack(pady=(0, 5), anchor="w", padx=10)
        
        cookie_textbox = ctk.CTkTextbox(
            main_frame,
            width=350,
            height=150,
            placeholder_text="请粘贴从浏览器复制的Cookie信息"
        )
        cookie_textbox.pack(pady=(0, 10), padx=10)
        
        # 提示信息
        hint_label = ctk.CTkLabel(
            main_frame,
            text="💡 提示：从浏览器开发者工具中复制Cookie\n支持多种格式：key=value; 或单个token",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        hint_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        def perform_login():
            """执行登录"""
            email = email_entry.get().strip()
            cookie_string = cookie_textbox.get("1.0", "end").strip()
            
            if not email:
                messagebox.showerror("错误", "请输入邮箱地址！")
                return
            
            if not cookie_string:
                messagebox.showerror("错误", "请输入Cookie信息！")
                return
            
            # 验证登录
            self.log_callback(f"🔍 验证登录信息: {email}", "INFO")
            
            try:
                success, message = quick_validate_login(email, cookie_string)
                
                if success:
                    # 保存登录信息
                    from utils.account_manager import save_login_info
                    if save_login_info(email, cookie_string):
                        self.log_callback("✅ 登录成功！", "SUCCESS")
                        
                        # 获取订阅信息
                        sub_success, sub_info = get_account_subscription_info(cookie_string)
                        
                        success_msg = f"账号 {email} 登录成功！\n\n"
                        if sub_success and sub_info.get('remaining_count'):
                            success_msg += f"⚡ 剩余使用次数: {sub_info['remaining_count']} 次\n"
                        
                        messagebox.showinfo("登录成功", success_msg)
                        
                        # 关闭登录窗口
                        login_window.destroy()
                        
                        # 刷新账号状态
                        self.refresh_account_status()
                    else:
                        messagebox.showerror("保存失败", "登录验证成功，但保存失败！")
                else:
                    messagebox.showerror("登录失败", f"登录验证失败：{message}")
                    
            except Exception as e:
                messagebox.showerror("错误", f"登录过程出错：{str(e)}")
        
        # 登录按钮
        login_btn = ctk.CTkButton(
            button_frame,
            text="🚀 登录",
            command=perform_login,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        login_btn.pack(side="left", padx=(10, 5), fill="x", expand=True)
        
        # 取消按钮
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ 取消",
            command=login_window.destroy,
            height=40
        )
        cancel_btn.pack(side="right", padx=(5, 10))
    
    def logout_account(self):
        """退出登录"""
        try:
            current_info = get_current_login_info()
            if not current_info:
                messagebox.showinfo("提示", "当前没有登录的账号")
                return
            
            # 确认退出
            if messagebox.askyesno("确认", f"确定要退出账号 {current_info['email']} 吗？"):
                account_manager = get_account_manager()
                account_manager.clear_all_accounts()
                
                self.log_callback("🚪 已退出登录", "INFO")
                messagebox.showinfo("成功", "已成功退出登录！")
                
                # 刷新账号状态
                self.refresh_account_status()
                
        except Exception as e:
            error_msg = f"退出登录失败: {str(e)}"
            self.log_callback(f"❌ {error_msg}", "ERROR")
            messagebox.showerror("错误", error_msg)
    
    def validate_current_cookie(self):
        """验证当前Cookie"""
        try:
            current_info = get_current_login_info()
            if not current_info:
                messagebox.showinfo("提示", "当前没有登录的账号")
                return
            
            self.log_callback("🔍 验证当前Cookie...", "INFO")
            
            email = current_info['email']
            cookie_string = current_info.get('cookie_string', '')
            
            success, message = quick_validate_login(email, cookie_string)
            
            if success:
                self.log_callback("✅ Cookie验证成功", "SUCCESS")
                messagebox.showinfo("验证成功", f"账号 {email} 的Cookie仍然有效！")
            else:
                self.log_callback(f"❌ Cookie验证失败: {message}", "ERROR")
                messagebox.showerror("验证失败", f"Cookie已失效：{message}\n\n请重新登录。")
                
        except Exception as e:
            error_msg = f"Cookie验证失败: {str(e)}"
            self.log_callback(f"❌ {error_msg}", "ERROR")
            messagebox.showerror("错误", error_msg)
    
    def refresh_subscription_info(self):
        """刷新订阅信息"""
        try:
            current_info = get_current_login_info()
            if not current_info:
                messagebox.showinfo("提示", "请先登录账号")
                return
            
            self.log_callback("📊 刷新订阅信息...", "INFO")
            
            # 只刷新订阅信息部分
            self._refresh_subscription_info()
            
            self.log_callback("✅ 订阅信息已刷新", "SUCCESS")
            
        except Exception as e:
            error_msg = f"刷新订阅信息失败: {str(e)}"
            self.log_callback(f"❌ {error_msg}", "ERROR")
            messagebox.showerror("错误", error_msg)
    
    # 将方法绑定到实例
    self.show_login_dialog = show_login_dialog.__get__(self, self.__class__)
    self.logout_account = logout_account.__get__(self, self.__class__)
    self.validate_current_cookie = validate_current_cookie.__get__(self, self.__class__)
    self.refresh_subscription_info = refresh_subscription_info.__get__(self, self.__class__)
