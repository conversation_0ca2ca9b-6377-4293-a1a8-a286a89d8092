#!/usr/bin/env python3
"""
测试账号状态页面的功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from utils.account_manager import get_current_login_info, get_account_subscription_info
from utils.account_detector import get_all_account_info
from utils.cookie_login import quick_validate_login

def test_account_status_features():
    """测试账号状态页面的各项功能"""
    print("🧪 测试账号状态页面功能")
    print("=" * 50)
    
    # 1. 测试登录状态检查
    print("\n1️⃣ 测试登录状态检查")
    print("-" * 30)
    
    try:
        current_info = get_current_login_info()
        if current_info:
            print(f"✅ 检测到登录账号: {current_info['email']}")
            print(f"👤 用户名: {current_info['username']}")
            print(f"🕒 登录时间: {current_info['last_used']}")
            print(f"🍪 Cookie长度: {len(current_info.get('cookie_string', ''))} 字符")
        else:
            print("❌ 未检测到登录账号")
    except Exception as e:
        print(f"❌ 登录状态检查失败: {e}")
    
    # 2. 测试订阅信息获取
    print("\n2️⃣ 测试订阅信息获取")
    print("-" * 30)
    
    try:
        success, sub_info = get_account_subscription_info()
        if success:
            print("✅ 订阅信息获取成功")
            if sub_info.get('plan_name'):
                print(f"📋 订阅计划: {sub_info['plan_name']}")
            
            if sub_info.get('usage_limit', 0) > 0:
                usage_count = sub_info.get('usage_count', 0)
                usage_limit = sub_info.get('usage_limit', 0)
                remaining = sub_info.get('remaining_count', 0)
                usage_percent = (usage_count / usage_limit) * 100 if usage_limit > 0 else 0
                
                print(f"🔢 使用情况: {usage_count}/{usage_limit} ({usage_percent:.1f}%)")
                print(f"⚡ 剩余次数: {remaining} 次")
                
                # 状态评估
                if remaining > 50:
                    status = "🟢 充足"
                elif remaining > 10:
                    status = "🟡 注意"
                else:
                    status = "🔴 紧急"
                print(f"📊 状态评估: {status}")
            else:
                print(f"🔢 已使用: {sub_info.get('usage_count', 0)} 次")
            
            if sub_info.get('reset_date'):
                print(f"🔄 重置日期: {sub_info['reset_date']}")
                
        else:
            print(f"❌ 订阅信息获取失败: {sub_info.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ 订阅信息查询异常: {e}")
    
    # 3. 测试系统检测
    print("\n3️⃣ 测试系统检测")
    print("-" * 30)
    
    try:
        account_info = get_all_account_info()
        augment_account = account_info['augment_account']
        augment_activity = account_info['augment_activity']
        
        # 系统检测状态
        if augment_account['logged_in']:
            print("✅ 系统检测到AugmentCode登录")
            
            if augment_account['email']:
                print(f"📧 检测邮箱: {augment_account['email']}")
            
            if augment_account['username']:
                print(f"👤 检测用户名: {augment_account['username']}")
            
            if augment_account['login_time']:
                print(f"🕒 检测登录时间: {augment_account['login_time']}")
            
            login_method = augment_account.get('login_method', 'unknown')
            print(f"🔐 登录方式: {login_method}")
            
            # Cookie信息
            cookie_info = augment_account.get('cookie_info', {})
            if cookie_info.get('has_cookie'):
                print(f"🍪 Cookie状态: 已检测到 ({cookie_info['cookie_length']} 字符)")
            else:
                print("🍪 Cookie状态: 未检测到")
        else:
            print("❌ 系统未检测到AugmentCode登录")
        
        # 活动状态
        if augment_activity['is_active']:
            print("🚀 检测到AugmentCode活动")
            
            activity_details = []
            if augment_activity.get('extension_confirmed'):
                activity_details.append("扩展确认")
            if augment_activity.get('publisher_trusted'):
                activity_details.append("发布者信任")
            if augment_activity.get('chat_history'):
                activity_details.append("聊天使用")
            if augment_activity.get('webview_sessions'):
                session_count = len(augment_activity['webview_sessions'])
                activity_details.append(f"Webview会话({session_count})")
            
            recent_activity = augment_activity.get('recent_activity', [])
            if recent_activity:
                activity_details.append(f"最近活动({len(recent_activity)})")
            
            if activity_details:
                print(f"📊 活动详情: {', '.join(activity_details)}")
        else:
            print("😴 未检测到明显的AugmentCode活动")
            
    except Exception as e:
        print(f"❌ 系统检测失败: {e}")
    
    # 4. 测试Cookie验证（如果有登录信息）
    print("\n4️⃣ 测试Cookie验证")
    print("-" * 30)
    
    try:
        current_info = get_current_login_info()
        if current_info:
            email = current_info['email']
            cookie_string = current_info.get('cookie_string', '')
            
            print(f"🔍 验证账号 {email} 的Cookie...")
            success, message = quick_validate_login(email, cookie_string)
            
            if success:
                print("✅ Cookie验证成功")
                
                # 获取最新订阅信息
                sub_success, sub_info = get_account_subscription_info(cookie_string)
                if sub_success and sub_info.get('remaining_count') is not None:
                    print(f"⚡ 当前剩余使用次数: {sub_info['remaining_count']} 次")
            else:
                print(f"❌ Cookie验证失败: {message}")
        else:
            print("⚠️ 没有登录信息可供验证")
    except Exception as e:
        print(f"❌ Cookie验证失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("\n💡 GUI界面特色:")
    print("• 标签页设计，信息分类清晰")
    print("• 实时显示剩余使用次数")
    print("• 颜色编码状态指示（绿色=充足，橙色=注意，红色=紧急）")
    print("• 一键刷新所有状态信息")
    print("• 集成Cookie验证功能")
    print("• 主页简化显示，详细信息在专门的账号状态页面")

def main():
    """主函数"""
    print("🎯 Free AugmentCode - 账号状态功能测试")
    print("=" * 60)
    
    try:
        test_account_status_features()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
