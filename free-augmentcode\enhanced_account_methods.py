#!/usr/bin/env python3
"""
增强的账号信息获取方法
基于开源技术和最佳实践
"""

import sys
import os
import requests
import json
import time
import random
from typing import Dict, Optional, Tuple, List
from datetime import datetime
import urllib.parse

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

class EnhancedAccountInfoGetter:
    """增强的账号信息获取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://app.augmentcode.com"
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
    def setup_session(self, cookies: Dict[str, str], use_random_ua: bool = True):
        """
        设置会话，包含反检测技术
        
        Args:
            cookies: Cookie字典
            use_random_ua: 是否使用随机User-Agent
        """
        # 设置Cookie
        for key, value in cookies.items():
            self.session.cookies.set(key, value)
        
        # 随机选择User-Agent
        user_agent = random.choice(self.user_agents) if use_random_ua else self.user_agents[0]
        
        # 设置请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'Referer': 'https://app.augmentcode.com/',
            'Origin': 'https://app.augmentcode.com'
        })
    
    def method_1_direct_api(self, cookie_string: str) -> Tuple[bool, Dict]:
        """
        方法1: 直接API调用
        尝试各种可能的API端点
        """
        print("🔌 方法1: 尝试直接API调用")
        
        cookies = self._parse_cookie_string(cookie_string)
        self.setup_session(cookies)
        
        # 可能的API端点
        api_endpoints = [
            '/api/account',
            '/api/user',
            '/api/subscription',
            '/api/account/subscription',
            '/api/user/subscription',
            '/api/me',
            '/api/profile',
            '/api/dashboard',
            '/api/usage',
            '/api/billing',
            '/account/api/subscription',
            '/user/api/subscription'
        ]
        
        for endpoint in api_endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                print(f"  📡 测试: {endpoint}")
                
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"  ✅ 成功获取JSON数据")
                        return True, self._parse_api_data(data)
                    except json.JSONDecodeError:
                        print(f"  📄 非JSON响应")
                        continue
                elif response.status_code == 401:
                    print(f"  🔐 需要认证")
                elif response.status_code == 403:
                    print(f"  🚫 访问被拒绝")
                else:
                    print(f"  ❌ 状态码: {response.status_code}")
                    
                # 添加随机延迟避免被检测
                time.sleep(random.uniform(0.5, 1.5))
                
            except Exception as e:
                print(f"  ❌ 异常: {e}")
                continue
        
        return False, {'error': '所有API端点都失败'}
    
    def method_2_page_scraping(self, cookie_string: str) -> Tuple[bool, Dict]:
        """
        方法2: 页面抓取
        使用增强的HTML解析技术
        """
        print("🌐 方法2: 增强页面抓取")
        
        cookies = self._parse_cookie_string(cookie_string)
        self.setup_session(cookies)
        
        # 要尝试的页面
        pages = [
            '/account/subscription',
            '/account',
            '/dashboard',
            '/profile',
            '/billing',
            '/usage'
        ]
        
        for page in pages:
            try:
                url = f"{self.base_url}{page}"
                print(f"  📄 抓取页面: {page}")
                
                response = self.session.get(url, timeout=15)
                
                if response.status_code == 200:
                    # 检查是否被重定向到登录页面
                    if 'login' in response.url.lower() or 'signin' in response.url.lower():
                        print(f"  🔄 被重定向到登录页面")
                        continue
                    
                    # 解析页面内容
                    parsed_data = self._enhanced_html_parsing(response.text)
                    
                    if parsed_data and any(parsed_data.values()):
                        print(f"  ✅ 成功解析页面数据")
                        return True, parsed_data
                    else:
                        print(f"  📄 页面无有效数据")
                
                # 添加随机延迟
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                print(f"  ❌ 页面抓取异常: {e}")
                continue
        
        return False, {'error': '页面抓取失败'}
    
    def method_3_browser_automation(self, cookie_string: str) -> Tuple[bool, Dict]:
        """
        方法3: 浏览器自动化
        使用Selenium模拟真实浏览器行为
        """
        print("🤖 方法3: 浏览器自动化")
        
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={random.choice(self.user_agents)}')
            
            # 启动浏览器
            driver = webdriver.Chrome(options=chrome_options)
            
            try:
                # 访问主页
                driver.get(self.base_url)
                
                # 添加Cookie
                cookies = self._parse_cookie_string(cookie_string)
                for key, value in cookies.items():
                    driver.add_cookie({'name': key, 'value': value})
                
                # 访问订阅页面
                driver.get(f"{self.base_url}/account/subscription")
                
                # 等待页面加载
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 获取页面源码
                page_source = driver.page_source
                
                # 解析页面内容
                parsed_data = self._enhanced_html_parsing(page_source)
                
                if parsed_data and any(parsed_data.values()):
                    print("  ✅ 浏览器自动化成功")
                    return True, parsed_data
                else:
                    print("  📄 浏览器自动化无有效数据")
                    
            finally:
                driver.quit()
                
        except ImportError:
            print("  ⚠️ Selenium未安装，跳过浏览器自动化")
        except Exception as e:
            print(f"  ❌ 浏览器自动化异常: {e}")
        
        return False, {'error': '浏览器自动化失败'}
    
    def method_4_network_analysis(self, cookie_string: str) -> Tuple[bool, Dict]:
        """
        方法4: 网络流量分析
        分析网络请求模式，寻找隐藏的API
        """
        print("🔍 方法4: 网络流量分析")
        
        cookies = self._parse_cookie_string(cookie_string)
        self.setup_session(cookies)
        
        # 模拟浏览器行为序列
        behavior_sequence = [
            '/',  # 首页
            '/account',  # 账号页面
            '/account/subscription',  # 订阅页面
        ]
        
        collected_data = {}
        
        for page in behavior_sequence:
            try:
                url = f"{self.base_url}{page}"
                print(f"  🌐 分析页面: {page}")
                
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    # 分析响应头
                    headers = dict(response.headers)
                    
                    # 查找可能的API端点
                    content = response.text
                    api_patterns = self._extract_api_patterns(content)
                    
                    if api_patterns:
                        print(f"  🔍 发现API模式: {len(api_patterns)} 个")
                        
                        # 尝试调用发现的API
                        for api_url in api_patterns:
                            try:
                                api_response = self.session.get(api_url, timeout=5)
                                if api_response.status_code == 200:
                                    try:
                                        api_data = api_response.json()
                                        collected_data.update(self._parse_api_data(api_data))
                                        print(f"    ✅ API调用成功: {api_url}")
                                    except:
                                        pass
                            except:
                                pass
                
                time.sleep(random.uniform(0.5, 1))
                
            except Exception as e:
                print(f"  ❌ 网络分析异常: {e}")
                continue
        
        if collected_data:
            return True, collected_data
        
        return False, {'error': '网络分析未发现有效数据'}
    
    def get_account_info_comprehensive(self, cookie_string: str) -> Tuple[bool, Dict]:
        """
        综合方法获取账号信息
        按优先级尝试所有方法
        """
        print("🎯 开始综合账号信息获取")
        print("=" * 50)
        
        methods = [
            self.method_1_direct_api,
            self.method_2_page_scraping,
            self.method_4_network_analysis,
            self.method_3_browser_automation,  # 最后尝试，因为资源消耗大
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                print(f"\n🔄 尝试方法 {i}/4")
                success, data = method(cookie_string)
                
                if success and data and not data.get('error'):
                    print(f"✅ 方法 {i} 成功获取数据")
                    return True, data
                else:
                    print(f"❌ 方法 {i} 失败: {data.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ 方法 {i} 异常: {e}")
                continue
        
        print("\n❌ 所有方法都失败了")
        return False, {'error': '所有获取方法都失败'}
    
    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        
        try:
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for part in parts:
                    if '=' in part:
                        key, value = part.strip().split('=', 1)
                        cookies[key.strip()] = value.strip()
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()
            else:
                # 尝试常见的Cookie名称
                common_names = ['auth_token', 'access_token', 'session', 'token', 'jwt']
                for name in common_names:
                    cookies[name] = cookie_string.strip()
        except:
            pass
            
        return cookies
    
    def _parse_api_data(self, data: Dict) -> Dict:
        """解析API数据"""
        result = {
            'plan_name': '',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '',
            'subscription_status': 'active'
        }
        
        # 尝试不同的数据结构
        if isinstance(data, dict):
            # 直接字段
            result['plan_name'] = data.get('plan_name', data.get('plan', data.get('subscription_plan', '')))
            result['usage_count'] = data.get('usage_count', data.get('used', data.get('messages_used', 0)))
            result['usage_limit'] = data.get('usage_limit', data.get('limit', data.get('messages_limit', 0)))
            result['reset_date'] = data.get('reset_date', data.get('next_reset', data.get('billing_date', '')))
            
            # 嵌套结构
            if 'subscription' in data:
                sub_data = data['subscription']
                result.update(self._parse_api_data(sub_data))
            
            if 'account' in data:
                acc_data = data['account']
                result.update(self._parse_api_data(acc_data))
            
            # 计算剩余次数
            if result['usage_limit'] > 0:
                result['remaining_count'] = max(0, result['usage_limit'] - result['usage_count'])
        
        return result
    
    def _enhanced_html_parsing(self, html: str) -> Dict:
        """增强的HTML解析"""
        result = {
            'plan_name': '',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '',
            'subscription_status': 'active'
        }
        
        try:
            import re
            
            # 更全面的正则表达式模式
            patterns = {
                'available': [
                    r'(\d+(?:\.\d+)?)\s+available',
                    r'available[:\s]*(\d+(?:\.\d+)?)',
                    r'剩余[:\s]*(\d+(?:\.\d+)?)',
                ],
                'usage': [
                    r'Used\s+(\d+)\s+of\s+(\d+)',
                    r'已使用\s*(\d+)\s*/\s*(\d+)',
                    r'(\d+)\s*/\s*(\d+)\s*(?:messages?|requests?|uses?)',
                ],
                'plan': [
                    r'(Community\s+Plan)',
                    r'(Pro\s+Plan)',
                    r'(Enterprise\s+Plan)',
                    r'(Free\s+Plan)',
                    r'(Basic\s+Plan)',
                    r'Current\s+plan[^>]*>([^<]+)',
                ],
                'date': [
                    r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d+,\s+\d{4}',
                    r'(\d{4}-\d{2}-\d{2})',
                    r'Next\s+Billing\s+Date[^>]*>([^<]+)',
                ],
                'renew': [
                    r'(\d+)\s+renew\s+monthly',
                    r'每月续费\s*(\d+)',
                ]
            }
            
            # 应用所有模式
            for category, pattern_list in patterns.items():
                for pattern in pattern_list:
                    matches = re.findall(pattern, html, re.IGNORECASE)
                    if matches:
                        if category == 'available':
                            value = float(matches[0])
                            result['remaining_count'] = int(value) if value.is_integer() else value
                        elif category == 'usage' and len(matches[0]) == 2:
                            result['usage_count'] = int(matches[0][0])
                            result['usage_limit'] = int(matches[0][1])
                        elif category == 'plan':
                            result['plan_name'] = matches[0].strip()
                        elif category == 'date':
                            result['reset_date'] = matches[0] if isinstance(matches[0], str) else matches[0][0]
                        elif category == 'renew':
                            if result['usage_limit'] == 0:
                                result['usage_limit'] = int(matches[0])
                        break
            
            # 数据一致性检查
            if result['usage_limit'] > 0 and result['remaining_count'] == 0:
                result['remaining_count'] = max(0, result['usage_limit'] - result['usage_count'])
            
        except Exception as e:
            result['parse_error'] = str(e)
        
        return result
    
    def _extract_api_patterns(self, content: str) -> List[str]:
        """从页面内容中提取可能的API端点"""
        import re
        
        api_patterns = []
        
        # 查找API URL模式
        patterns = [
            r'["\']([^"\']*api[^"\']*)["\']',
            r'fetch\(["\']([^"\']+)["\']',
            r'axios\.get\(["\']([^"\']+)["\']',
            r'\.get\(["\']([^"\']+)["\']',
            r'url["\']?\s*:\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if 'api' in match.lower() or 'subscription' in match.lower():
                    if match.startswith('/'):
                        api_patterns.append(f"{self.base_url}{match}")
                    elif match.startswith('http'):
                        api_patterns.append(match)
        
        return list(set(api_patterns))  # 去重


# 快速调用函数
def get_enhanced_account_info(cookie_string: str) -> Tuple[bool, Dict]:
    """
    快速获取增强的账号信息
    
    Args:
        cookie_string: Cookie字符串
        
    Returns:
        tuple: (是否成功, 账号信息)
    """
    getter = EnhancedAccountInfoGetter()
    return getter.get_account_info_comprehensive(cookie_string)


if __name__ == "__main__":
    # 测试代码
    from utils.account_manager import get_current_login_info
    
    current_info = get_current_login_info()
    if current_info:
        cookie_string = current_info.get('cookie_string', '')
        if cookie_string:
            success, data = get_enhanced_account_info(cookie_string)
            print(f"\n🎯 最终结果: {'成功' if success else '失败'}")
            print(f"📊 数据: {data}")
        else:
            print("❌ 未找到Cookie信息")
    else:
        print("❌ 未找到登录信息")
