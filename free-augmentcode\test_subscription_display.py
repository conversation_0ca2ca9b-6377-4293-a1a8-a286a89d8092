#!/usr/bin/env python3
"""
测试订阅信息显示功能
"""

from utils.account_manager import (
    get_current_login_info, get_account_subscription_info, 
    format_account_status
)
from utils.subscription_checker import get_subscription_checker

def test_subscription_display():
    """测试订阅信息显示"""
    print("🧪 测试订阅信息显示功能")
    print("=" * 60)
    
    # 1. 测试当前登录信息
    print("1️⃣ 测试当前登录信息:")
    current_info = get_current_login_info()
    if current_info:
        print(f"  ✅ 找到登录信息:")
        print(f"    📧 邮箱: {current_info['email']}")
        print(f"    👤 用户名: {current_info['username']}")
        print(f"    🕒 最后使用: {current_info['last_used']}")
        print(f"    🍪 Cookie长度: {len(current_info.get('cookie_string', ''))} 字符")
    else:
        print("  ❌ 未找到登录信息")
        return
    
    print()
    
    # 2. 测试订阅信息获取
    print("2️⃣ 测试订阅信息获取:")
    try:
        success, sub_info = get_account_subscription_info()
        print(f"  获取结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print(f"  📊 订阅详情:")
            print(f"    📋 计划名称: {sub_info.get('plan_name', 'N/A')}")
            print(f"    🔢 已使用: {sub_info.get('usage_count', 0)} 次")
            print(f"    📊 使用限制: {sub_info.get('usage_limit', 0)} 次")
            print(f"    ⚡ 剩余次数: {sub_info.get('remaining_count', 0)} 次")
            print(f"    🔄 重置日期: {sub_info.get('reset_date', 'N/A')}")
            print(f"    ✅ 状态: {sub_info.get('subscription_status', 'N/A')}")
        else:
            print(f"  ❌ 错误信息: {sub_info.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"  ❌ 异常: {str(e)}")
    
    print()
    
    # 3. 测试格式化状态信息
    print("3️⃣ 测试格式化状态信息:")
    try:
        formatted_status = format_account_status(include_subscription=True)
        print("  📝 格式化结果:")
        print("  " + "\n  ".join(formatted_status.split('\n')))
    except Exception as e:
        print(f"  ❌ 格式化失败: {str(e)}")
    
    print()
    
    # 4. 测试订阅检查器直接调用
    print("4️⃣ 测试订阅检查器直接调用:")
    try:
        checker = get_subscription_checker()
        cookie_string = current_info.get('cookie_string', '')
        
        if cookie_string:
            direct_result = checker.get_subscription_info(cookie_string)
            print(f"  直接调用结果: {'✅ 成功' if direct_result.get('success') else '❌ 失败'}")
            
            if direct_result.get('success'):
                formatted_info = checker.format_subscription_info(direct_result)
                print("  📊 格式化订阅信息:")
                print("  " + "\n  ".join(formatted_info.split('\n')))
            else:
                print(f"  ❌ 错误: {direct_result.get('error', '未知错误')}")
        else:
            print("  ❌ 没有Cookie信息可供测试")
            
    except Exception as e:
        print(f"  ❌ 直接调用失败: {str(e)}")
    
    print()
    
    # 5. 模拟不同的订阅状态
    print("5️⃣ 模拟不同订阅状态的显示效果:")
    
    # 模拟数据
    mock_scenarios = [
        {
            'name': '免费计划 - 剩余较多',
            'data': {
                'success': True,
                'plan_name': 'Free Plan',
                'usage_count': 25,
                'usage_limit': 100,
                'remaining_count': 75,
                'reset_date': '2025-07-01',
                'subscription_status': 'active'
            }
        },
        {
            'name': '付费计划 - 剩余较少',
            'data': {
                'success': True,
                'plan_name': 'Pro Plan',
                'usage_count': 950,
                'usage_limit': 1000,
                'remaining_count': 50,
                'reset_date': '2025-06-30',
                'subscription_status': 'active'
            }
        },
        {
            'name': '即将用完',
            'data': {
                'success': True,
                'plan_name': 'Basic Plan',
                'usage_count': 495,
                'usage_limit': 500,
                'remaining_count': 5,
                'reset_date': '2025-06-25',
                'subscription_status': 'active'
            }
        },
        {
            'name': '获取失败',
            'data': {
                'success': False,
                'error': '网络连接超时'
            }
        }
    ]
    
    for scenario in mock_scenarios:
        print(f"  📋 场景: {scenario['name']}")
        
        # 模拟GUI显示逻辑
        sub_info = scenario['data']
        if sub_info.get('success'):
            print(f"    📊 订阅信息:")
            if sub_info.get('plan_name'):
                print(f"      📋 计划: {sub_info['plan_name']}")
            
            if sub_info.get('usage_limit', 0) > 0:
                usage_count = sub_info.get('usage_count', 0)
                usage_limit = sub_info.get('usage_limit', 0)
                remaining = sub_info.get('remaining_count', 0)
                usage_percent = (usage_count / usage_limit) * 100 if usage_limit > 0 else 0
                
                print(f"      🔢 使用: {usage_count}/{usage_limit} ({usage_percent:.1f}%)")
                
                # 根据剩余量设置颜色提示
                if remaining > 50:
                    color_hint = "🟢 充足"
                elif remaining > 10:
                    color_hint = "🟡 注意"
                else:
                    color_hint = "🔴 紧急"
                    
                print(f"      ⚡ 剩余: {remaining} 次 {color_hint}")
            
            if sub_info.get('reset_date'):
                print(f"      🔄 重置: {sub_info['reset_date']}")
        else:
            print(f"    ⚠️ 订阅信息: {sub_info.get('error', '获取失败')}")
        
        print()

def main():
    """主函数"""
    try:
        test_subscription_display()
        print("✅ 测试完成！")
        
        print("\n💡 使用建议:")
        print("1. 如果订阅信息获取成功，GUI将显示详细的使用情况")
        print("2. 剩余次数会根据数量显示不同颜色（绿色=充足，橙色=注意，红色=紧急）")
        print("3. 如果获取失败，会显示错误信息但不影响其他功能")
        print("4. 订阅信息会在登录成功时自动获取并显示")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
