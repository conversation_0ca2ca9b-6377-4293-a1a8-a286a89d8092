#!/usr/bin/env python3
"""
测试扩展检查功能
"""

from utils.extension_checker import get_augment_extension_info, format_extension_info, find_augment_related_extensions
from utils.paths import get_extensions_path

def main():
    print("🔍 测试 AugmentCode 扩展检查功能")
    print("=" * 50)
    
    # 检查扩展路径
    ext_path = get_extensions_path()
    print(f"📁 扩展目录: {ext_path}")
    
    import os
    if os.path.exists(ext_path):
        print("✅ 扩展目录存在")
        try:
            items = os.listdir(ext_path)
            print(f"📊 扩展数量: {len(items)}")
            
            # 查找包含augment的目录
            augment_dirs = [item for item in items if 'augment' in item.lower()]
            if augment_dirs:
                print(f"🔍 找到可能的AugmentCode扩展目录: {augment_dirs}")
            else:
                print("❌ 未找到AugmentCode相关目录")
        except Exception as e:
            print(f"❌ 读取扩展目录失败: {e}")
    else:
        print("❌ 扩展目录不存在")
    
    print("\n" + "=" * 50)
    
    # 检查AugmentCode扩展信息
    print("🔍 检查 AugmentCode 扩展信息...")
    try:
        ext_info = get_augment_extension_info()
        print(format_extension_info(ext_info))
    except Exception as e:
        print(f"❌ 检查扩展信息失败: {e}")
    
    print("\n" + "=" * 50)
    
    # 查找相关扩展
    print("🔍 查找相关扩展...")
    try:
        related_exts = find_augment_related_extensions()
        if related_exts:
            for ext in related_exts:
                print(f"- {ext['display_name']} v{ext['version']} by {ext['publisher']}")
        else:
            print("❌ 未找到相关扩展")
    except Exception as e:
        print(f"❌ 查找相关扩展失败: {e}")

if __name__ == "__main__":
    main()
