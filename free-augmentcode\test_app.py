#!/usr/bin/env python3
"""
应用程序测试脚本
测试各个模块的基本功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from gui.modern_ui import ModernUI
        print("✅ ModernUI 导入成功")
    except Exception as e:
        print(f"❌ ModernUI 导入失败: {e}")
        return False
    
    try:
        from gui.dashboard_view import DashboardView
        print("✅ DashboardView 导入成功")
    except Exception as e:
        print(f"❌ DashboardView 导入失败: {e}")
        return False
    
    try:
        from gui.detection_view import DetectionView
        print("✅ DetectionView 导入成功")
    except Exception as e:
        print(f"❌ DetectionView 导入失败: {e}")
        return False
    
    try:
        from gui.cleanup_view import CleanupView
        print("✅ CleanupView 导入成功")
    except Exception as e:
        print(f"❌ CleanupView 导入失败: {e}")
        return False
    
    try:
        from gui.account_view import AccountView
        print("✅ AccountView 导入成功")
    except Exception as e:
        print(f"❌ AccountView 导入失败: {e}")
        return False
    
    try:
        from utils.account_detector import get_all_account_info
        print("✅ account_detector 导入成功")
    except Exception as e:
        print(f"❌ account_detector 导入失败: {e}")
        return False
    
    try:
        from utils.workspace_cleaner import WorkspaceCleaner
        print("✅ workspace_cleaner 导入成功")
    except Exception as e:
        print(f"❌ workspace_cleaner 导入失败: {e}")
        return False
    
    return True

def test_account_detector():
    """测试账号检测器"""
    print("\n测试账号检测器...")
    
    try:
        from utils.account_detector import get_all_account_info
        
        # 测试获取账号信息
        info = get_all_account_info()
        print(f"✅ 账号检测器运行成功")
        print(f"   检测结果: {type(info)} 类型")
        
        if isinstance(info, dict):
            print(f"   包含键: {list(info.keys())}")
        
        return True
    except Exception as e:
        print(f"❌ 账号检测器测试失败: {e}")
        return False

def test_workspace_cleaner():
    """测试工作区清理器"""
    print("\n测试工作区清理器...")
    
    try:
        from utils.workspace_cleaner import WorkspaceCleaner
        
        cleaner = WorkspaceCleaner()
        print("✅ 工作区清理器创建成功")
        
        # 测试扫描当前目录
        current_dir = os.getcwd()
        results = cleaner.scan_directory(current_dir)
        print(f"✅ 目录扫描成功")
        print(f"   扫描结果: {len(results)} 个类别")
        
        for category, files in results.items():
            print(f"   {category}: {len(files)} 个文件")
        
        return True
    except Exception as e:
        print(f"❌ 工作区清理器测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件（不启动GUI）"""
    print("\n测试UI组件...")
    
    try:
        import customtkinter as ctk
        
        # 创建一个测试窗口（不显示）
        root = ctk.CTk()
        root.withdraw()  # 隐藏窗口
        
        # 测试创建各个视图组件
        test_frame = ctk.CTkFrame(root)
        
        from gui.dashboard_view import DashboardView
        dashboard = DashboardView(test_frame)
        print("✅ DashboardView 创建成功")
        
        from gui.detection_view import DetectionView
        detection = DetectionView(test_frame)
        print("✅ DetectionView 创建成功")
        
        from gui.cleanup_view import CleanupView
        cleanup = CleanupView(test_frame)
        print("✅ CleanupView 创建成功")
        
        from gui.account_view import AccountView
        account = AccountView(test_frame)
        print("✅ AccountView 创建成功")
        
        # 清理
        root.destroy()
        
        return True
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始应用程序测试\n")
    
    tests = [
        ("模块导入", test_imports),
        ("账号检测器", test_account_detector),
        ("工作区清理器", test_workspace_cleaner),
        ("UI组件", test_ui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"测试: {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
        
        print()
    
    print(f"{'='*50}")
    print(f"测试总结: {passed}/{total} 个测试通过")
    print(f"{'='*50}")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序可以正常运行。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
