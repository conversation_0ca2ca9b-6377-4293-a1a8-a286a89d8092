#!/usr/bin/env python3
"""
调试订阅信息请求
"""

import sys
import os
import requests

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from utils.account_manager import get_current_login_info
from utils.subscription_checker import SubscriptionChecker

def debug_subscription_request():
    """调试订阅信息请求"""
    print("🔍 调试订阅信息请求")
    print("=" * 50)
    
    # 获取当前登录信息
    current_info = get_current_login_info()
    if not current_info:
        print("❌ 未找到登录信息")
        return
    
    email = current_info['email']
    cookie_string = current_info.get('cookie_string', '')
    
    print(f"👤 账号: {email}")
    print(f"🍪 Cookie长度: {len(cookie_string)} 字符")
    print(f"🍪 Cookie前50字符: {cookie_string[:50]}...")
    
    # 创建订阅检查器
    checker = SubscriptionChecker()
    
    # 解析Cookie
    cookies = checker._parse_cookie_string(cookie_string)
    print(f"\n🔧 解析的Cookie数量: {len(cookies)}")
    for key, value in cookies.items():
        print(f"  {key}: {value[:20]}..." if len(value) > 20 else f"  {key}: {value}")
    
    # 设置会话
    checker.setup_session(cookies)
    
    # 测试订阅页面访问
    print(f"\n🌐 测试订阅页面访问")
    print("-" * 30)
    
    try:
        url = "https://app.augmentcode.com/account/subscription"
        print(f"📡 请求URL: {url}")
        
        response = checker.session.get(url, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📏 响应内容长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            print("✅ 页面访问成功")
            
            # 检查是否包含登录相关的内容
            content = response.text.lower()
            
            # 检查关键词
            keywords = [
                'subscription', 'billing', 'plan', 'usage', 'available',
                'community plan', 'user messages', 'login', 'logout'
            ]
            
            found_keywords = []
            for keyword in keywords:
                if keyword in content:
                    found_keywords.append(keyword)
            
            print(f"🔍 找到的关键词: {', '.join(found_keywords)}")
            
            # 检查是否需要登录
            if 'login' in content and 'logout' not in content:
                print("⚠️ 页面可能需要重新登录")
            elif 'subscription' in content and 'plan' in content:
                print("✅ 页面包含订阅信息")
            else:
                print("❓ 页面内容不确定")
            
            # 尝试解析页面
            print(f"\n🔧 尝试解析页面内容")
            print("-" * 30)
            
            parsed_result = checker._parse_html_response(response.text)
            
            print(f"📋 解析结果:")
            for key, value in parsed_result.items():
                if key != 'parse_error':
                    print(f"  {key}: {value}")
            
            if parsed_result.get('parse_error'):
                print(f"⚠️ 解析错误: {parsed_result['parse_error']}")
            
            # 保存页面内容用于调试（前1000字符）
            print(f"\n📄 页面内容预览（前500字符）:")
            print("-" * 30)
            preview = response.text[:500].replace('\n', ' ').replace('\r', '')
            print(preview)
            
            if len(response.text) > 500:
                print("...")
                print(f"（总共 {len(response.text)} 字符）")
            
        elif response.status_code == 401:
            print("❌ 认证失败 - Cookie可能已过期")
        elif response.status_code == 403:
            print("❌ 访问被拒绝 - 权限不足")
        elif response.status_code == 404:
            print("❌ 页面不存在")
        else:
            print(f"❌ 请求失败 - 状态码: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 测试API端点
    print(f"\n🔌 测试API端点")
    print("-" * 30)
    
    api_endpoints = [
        '/api/account/subscription',
        '/api/user/subscription',
        '/api/subscription',
        '/account/subscription'
    ]
    
    for endpoint in api_endpoints:
        try:
            url = f"https://app.augmentcode.com{endpoint}"
            print(f"📡 测试: {endpoint}")
            
            response = checker.session.get(url, timeout=5)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  ✅ JSON响应: {len(str(data))} 字符")
                    print(f"  📊 数据键: {list(data.keys()) if isinstance(data, dict) else 'non-dict'}")
                except:
                    print(f"  📄 非JSON响应: {len(response.text)} 字符")
            else:
                print(f"  ❌ 失败")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

def main():
    """主函数"""
    print("🔍 订阅信息请求调试工具")
    print("=" * 60)
    
    try:
        debug_subscription_request()
        
        print("\n" + "=" * 60)
        print("🎯 调试完成！")
        
        print("\n💡 如果发现问题:")
        print("• 检查Cookie是否有效")
        print("• 确认网络连接正常")
        print("• 验证AugmentCode网站是否可访问")
        print("• 检查是否需要重新登录")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
