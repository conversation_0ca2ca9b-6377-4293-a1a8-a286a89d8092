#!/usr/bin/env python3
"""
检查AugmentCode的实际用户数据
"""

import sqlite3
import json
import os
import platform

def get_vscode_path():
    """获取VS Code配置路径"""
    if platform.system() == "Windows":
        return os.path.expandvars("%APPDATA%/Code")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Library/Application Support/Code")
    else:  # Linux
        return os.path.expanduser("~/.config/Code")

def check_augment_data():
    """检查AugmentCode的用户数据"""
    vscode_path = get_vscode_path()
    print(f"VS Code路径: {vscode_path}")
    print("=" * 60)
    
    # 1. 检查globalStorage中的augment数据
    print("\n1️⃣ 检查globalStorage中的Augment数据:")
    augment_storage = os.path.join(vscode_path, "User", "globalStorage", "augment.vscode-augment")
    if os.path.exists(augment_storage):
        print(f"✅ 找到Augment存储目录: {augment_storage}")
        
        # 递归查找所有文件
        for root, dirs, files in os.walk(augment_storage):
            for file in files:
                file_path = os.path.join(root, file)
                print(f"   📁 {file_path}")
                
                try:
                    if file.endswith('.json'):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            print(f"      内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    else:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            print(f"      内容: {content[:500]}...")
                except Exception as e:
                    print(f"      ❌ 读取失败: {e}")
    else:
        print("❌ 未找到Augment存储目录")
    
    # 2. 检查state.vscdb数据库
    print("\n2️⃣ 检查state.vscdb数据库:")
    db_path = os.path.join(vscode_path, "User", "globalStorage", "state.vscdb")
    if os.path.exists(db_path):
        print(f"✅ 找到数据库: {db_path}")
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查找所有包含augment的记录
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%' OR value LIKE '%augment%'")
            rows = cursor.fetchall()
            
            print(f"   找到 {len(rows)} 条Augment相关记录:")
            for i, (key, value) in enumerate(rows):
                print(f"   {i+1}. Key: {key}")
                if len(str(value)) > 300:
                    print(f"      Value: {value[:300]}...")
                else:
                    print(f"      Value: {value}")
                print()
            
            conn.close()
        except Exception as e:
            print(f"   ❌ 数据库查询失败: {e}")
    else:
        print("❌ 未找到数据库文件")
    
    # 3. 检查storage.json
    print("\n3️⃣ 检查storage.json:")
    storage_path = os.path.join(vscode_path, "User", "globalStorage", "storage.json")
    if os.path.exists(storage_path):
        print(f"✅ 找到storage.json: {storage_path}")
        
        try:
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)
            
            # 查找认证相关的键
            auth_keys = []
            for key in storage_data.keys():
                if any(term in key.lower() for term in ['auth', 'user', 'account', 'login', 'token', 'session']):
                    auth_keys.append(key)
            
            print(f"   找到 {len(auth_keys)} 个认证相关键:")
            for key in auth_keys:
                value = storage_data[key]
                print(f"   🔑 {key}:")
                if isinstance(value, dict):
                    print(f"      {json.dumps(value, indent=6, ensure_ascii=False)}")
                elif len(str(value)) > 200:
                    print(f"      {value[:200]}...")
                else:
                    print(f"      {value}")
                print()
                
        except Exception as e:
            print(f"   ❌ 读取storage.json失败: {e}")
    else:
        print("❌ 未找到storage.json文件")
    
    # 4. 检查用户设置
    print("\n4️⃣ 检查用户设置:")
    settings_path = os.path.join(vscode_path, "User", "settings.json")
    if os.path.exists(settings_path):
        print(f"✅ 找到settings.json: {settings_path}")
        
        try:
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            # 查找augment相关设置
            augment_settings = {}
            for key, value in settings.items():
                if 'augment' in key.lower():
                    augment_settings[key] = value
            
            if augment_settings:
                print(f"   找到 {len(augment_settings)} 个Augment设置:")
                for key, value in augment_settings.items():
                    print(f"   🔧 {key}: {value}")
            else:
                print("   ❌ 未找到Augment相关设置")
                
        except Exception as e:
            print(f"   ❌ 读取settings.json失败: {e}")
    else:
        print("❌ 未找到settings.json文件")

if __name__ == "__main__":
    check_augment_data()
