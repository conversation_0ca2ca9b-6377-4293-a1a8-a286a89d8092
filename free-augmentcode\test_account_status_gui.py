#!/usr/bin/env python3
"""
测试账号状态GUI页面
"""

import customtkinter as ctk
from gui import FreeAugmentCodeGUI
from utils.account_manager import get_current_login_info, get_account_subscription_info

def test_account_status_gui():
    """测试账号状态GUI页面"""
    print("🧪 测试账号状态GUI页面")
    print("=" * 50)
    
    # 检查当前登录状态
    current_info = get_current_login_info()
    if current_info:
        print(f"✅ 检测到登录账号: {current_info['email']}")
        
        # 检查订阅信息
        success, sub_info = get_account_subscription_info()
        if success:
            print(f"📊 订阅信息: {sub_info.get('plan_name', 'Unknown')}")
            if sub_info.get('remaining_count') is not None:
                print(f"⚡ 剩余次数: {sub_info['remaining_count']} 次")
        else:
            print(f"⚠️ 订阅信息获取失败: {sub_info.get('error', '未知错误')}")
    else:
        print("❌ 未检测到登录账号")
    
    print("\n🚀 启动GUI界面...")
    print("💡 请在GUI中切换到 '👤 账号状态' 标签页查看效果")
    
    # 启动GUI
    try:
        app = FreeAugmentCodeGUI()
        app.run()
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")

def main():
    """主函数"""
    print("🎯 Free AugmentCode - 账号状态页面测试")
    print("=" * 60)
    
    print("\n📋 功能说明:")
    print("1. 🔐 登录状态 - 显示当前登录的账号信息")
    print("2. 📊 订阅信息 - 显示使用次数、剩余次数等")
    print("3. 🔍 系统检测 - 显示VS Code中检测到的AugmentCode信息")
    print("4. 🎮 账号操作 - 提供登录、退出、验证等操作")
    
    print("\n🎨 界面特色:")
    print("• 标签页设计，信息分类清晰")
    print("• 实时显示剩余使用次数")
    print("• 颜色编码状态指示（绿色=充足，橙色=注意，红色=紧急）")
    print("• 一键刷新所有状态信息")
    print("• 集成Cookie验证功能")
    
    print("\n" + "=" * 60)
    
    try:
        test_account_status_gui()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
