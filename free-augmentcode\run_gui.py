#!/usr/bin/env python3
"""
简化的GUI启动脚本
解决路径和依赖问题
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import customtkinter
    except ImportError:
        missing_deps.append("customtkinter")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    if missing_deps:
        print("❌ 缺少依赖项:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请安装依赖项:")
        print("pip install customtkinter requests")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动 Free AugmentCode GUI")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 导入并启动GUI
        from gui import FreeAugmentCodeGUI
        
        print("✅ 正在启动GUI界面...")
        app = FreeAugmentCodeGUI()
        app.root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有文件都在正确位置")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
