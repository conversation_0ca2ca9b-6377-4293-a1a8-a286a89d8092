#!/usr/bin/env python3
"""
简单的Cookie获取工具（命令行版本）
"""

import sys
import os
import webbrowser
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

def print_guide():
    """打印获取指南"""
    print("🍪 AugmentCode Cookie获取指南")
    print("=" * 50)
    print()
    print("📋 步骤：")
    print("1️⃣ 我将为您打开AugmentCode网站")
    print("2️⃣ 请登录您的账号")
    print("3️⃣ 按F12打开开发者工具")
    print("4️⃣ 点击'Network'（网络）标签页")
    print("5️⃣ 刷新页面（F5）")
    print("6️⃣ 点击任意网络请求")
    print("7️⃣ 在'Request Headers'中找到'Cookie:'")
    print("8️⃣ 复制Cookie:后面的完整内容")
    print("9️⃣ 回到这里粘贴Cookie")
    print()

def open_website():
    """打开网站"""
    print("🌐 正在打开AugmentCode网站...")
    try:
        webbrowser.open("https://app.augmentcode.com")
        print("✅ 网站已在浏览器中打开")
        return True
    except Exception as e:
        print(f"❌ 无法打开网站: {e}")
        return False

def open_subscription_page():
    """打开订阅页面"""
    print("📊 正在打开订阅页面...")
    try:
        webbrowser.open("https://app.augmentcode.com/account/subscription")
        print("✅ 订阅页面已在浏览器中打开")
        return True
    except Exception as e:
        print(f"❌ 无法打开订阅页面: {e}")
        return False

def get_cookie_input():
    """获取Cookie输入"""
    print("\n🍪 请粘贴您的Cookie:")
    print("💡 提示：Cookie通常很长，确保复制完整")
    print("📝 粘贴后按回车确认:")
    print("-" * 50)
    
    cookie = input().strip()
    
    if not cookie:
        print("❌ Cookie不能为空")
        return None
    
    print(f"✅ 已接收Cookie（长度: {len(cookie)} 字符）")
    return cookie

def validate_cookie(cookie_string):
    """验证Cookie"""
    print("\n🔍 正在验证Cookie...")
    
    try:
        from cookie_validator_and_refresher import CookieValidator
        
        validator = CookieValidator()
        valid, validation_info = validator.validate_cookie(cookie_string)
        
        if valid:
            print("✅ Cookie验证成功！")
            print(f"📊 状态: {validation_info.get('status', '有效')}")
            print(f"🔢 响应码: {validation_info.get('response_code', 200)}")
            return True
        else:
            print("❌ Cookie验证失败")
            error_msg = validation_info.get('error', validation_info.get('status', '未知错误'))
            print(f"🔍 错误: {error_msg}")
            
            print("\n💡 可能的解决方案:")
            print("• 确保已登录AugmentCode")
            print("• 检查Cookie格式是否正确")
            print("• 尝试重新获取Cookie")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def save_cookie(cookie_string):
    """保存Cookie"""
    print("\n💾 保存Cookie信息")
    print("-" * 30)
    
    # 获取邮箱
    email = input("📧 请输入您的AugmentCode邮箱地址: ").strip()
    
    if not email:
        print("❌ 邮箱不能为空")
        return False
    
    if '@' not in email:
        print("❌ 邮箱格式不正确")
        return False
    
    try:
        from utils.account_manager import save_account_login
        
        # 使用邮箱前缀作为用户名
        username = email.split('@')[0]
        
        print(f"🔄 正在保存账号信息...")
        success = save_account_login(email, cookie_string, username)
        
        if success:
            print("✅ Cookie保存成功！")
            print(f"📧 账号: {email}")
            print(f"👤 用户名: {username}")
            print(f"🍪 Cookie长度: {len(cookie_string)} 字符")
            print("\n🎉 现在可以使用主程序查看账号信息了！")
            return True
        else:
            print("❌ Cookie保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 保存过程出错: {e}")
        return False

def test_saved_info():
    """测试保存的信息"""
    print("\n🧪 测试保存的账号信息...")
    
    try:
        from utils.account_manager import get_current_login_info, get_account_subscription_info
        
        # 获取登录信息
        login_info = get_current_login_info()
        if login_info:
            print("✅ 登录信息获取成功")
            print(f"📧 账号: {login_info['email']}")
            print(f"👤 用户名: {login_info['username']}")
            
            # 测试订阅信息
            print("\n🔄 测试订阅信息获取...")
            success, sub_info = get_account_subscription_info()
            
            if success:
                print("✅ 订阅信息获取成功")
                print(f"📋 计划: {sub_info.get('plan_name', '未知')}")
                print(f"⚡ 剩余次数: {sub_info.get('remaining_count', 0)}")
                
                if sub_info.get('remaining_count', 0) > 0:
                    print("🎉 成功获取到真实的订阅数据！")
                else:
                    print("⚠️ 获取到基础数据，可能需要进一步优化")
            else:
                print(f"❌ 订阅信息获取失败: {sub_info.get('error', '未知错误')}")
        else:
            print("❌ 未找到保存的登录信息")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")

def main():
    """主函数"""
    print("🎯 AugmentCode Cookie获取工具")
    print("=" * 60)
    
    try:
        # 显示指南
        print_guide()
        
        # 询问是否打开网站
        choice = input("是否现在打开AugmentCode网站？(y/n): ").lower().strip()
        
        if choice in ['y', 'yes', '是', '']:
            if not open_website():
                return
            
            # 等待用户登录
            input("\n⏳ 请在浏览器中登录，完成后按回车继续...")
            
            # 询问是否打开订阅页面
            choice = input("是否打开订阅页面以便获取Cookie？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是', '']:
                open_subscription_page()
                input("\n⏳ 订阅页面已打开，请按F12获取Cookie，完成后按回车继续...")
        
        # 获取Cookie
        while True:
            cookie = get_cookie_input()
            if cookie:
                break
            
            retry = input("\n是否重新输入Cookie？(y/n): ").lower().strip()
            if retry not in ['y', 'yes', '是', '']:
                print("👋 操作已取消")
                return
        
        # 验证Cookie
        print("\n" + "=" * 50)
        if not validate_cookie(cookie):
            retry = input("\nCookie验证失败，是否仍要保存？(y/n): ").lower().strip()
            if retry not in ['y', 'yes', '是', '']:
                print("👋 操作已取消")
                return
        
        # 保存Cookie
        print("\n" + "=" * 50)
        if save_cookie(cookie):
            # 测试保存的信息
            test_saved_info()
            
            print("\n" + "=" * 60)
            print("🎉 Cookie获取和保存完成！")
            print("\n💡 下一步操作:")
            print("• 运行 python gui.py 查看完整界面")
            print("• 在'👤 账号状态'标签页查看详细信息")
            print("• 如果数据不正确，可以重新运行此工具")
        else:
            print("\n❌ Cookie保存失败，请重试")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
