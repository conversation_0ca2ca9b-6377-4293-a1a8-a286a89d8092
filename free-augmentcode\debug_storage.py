#!/usr/bin/env python3
"""
调试storage.json内容
"""

import json
import os
from utils.paths import get_storage_path, get_db_path
import sqlite3

def debug_storage():
    """调试storage.json内容"""
    print("🔍 调试 storage.json 内容")
    print("=" * 60)
    
    storage_path = get_storage_path()
    
    try:
        with open(storage_path, 'r', encoding='utf-8') as f:
            storage_data = json.load(f)
        
        print(f"📊 Storage 文件包含 {len(storage_data)} 个键:")
        print()
        
        for i, (key, value) in enumerate(storage_data.items(), 1):
            print(f"{i:2d}. {key}")
            
            # 显示值的类型和部分内容
            if isinstance(value, dict):
                print(f"    类型: dict ({len(value)} 项)")
                if len(value) <= 5:
                    for k, v in value.items():
                        print(f"      {k}: {str(v)[:50]}{'...' if len(str(v)) > 50 else ''}")
                else:
                    print(f"      (包含 {len(value)} 个键，太多无法全部显示)")
            elif isinstance(value, list):
                print(f"    类型: list ({len(value)} 项)")
                if len(value) <= 3:
                    for item in value:
                        print(f"      - {str(item)[:50]}{'...' if len(str(item)) > 50 else ''}")
            elif isinstance(value, str):
                print(f"    类型: string")
                print(f"    值: {value[:100]}{'...' if len(value) > 100 else ''}")
            else:
                print(f"    类型: {type(value).__name__}")
                print(f"    值: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
            
            print()
            
    except Exception as e:
        print(f"❌ 读取 storage.json 失败: {e}")

def debug_database():
    """调试数据库内容"""
    print("\n" + "=" * 60)
    print("🔍 调试数据库内容")
    print("=" * 60)
    
    db_path = get_db_path()
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 数据库包含 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 查看ItemTable的内容
        if ('ItemTable',) in tables:
            print(f"\n📋 ItemTable 内容:")
            cursor.execute("SELECT COUNT(*) FROM ItemTable")
            count = cursor.fetchone()[0]
            print(f"总记录数: {count}")
            
            # 获取前20条记录
            cursor.execute("SELECT key, value FROM ItemTable LIMIT 20")
            rows = cursor.fetchall()
            
            print(f"\n前 {len(rows)} 条记录:")
            for i, (key, value) in enumerate(rows, 1):
                print(f"{i:2d}. {key}")
                if value:
                    print(f"    值: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
                else:
                    print(f"    值: (空)")
                print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 读取数据库失败: {e}")

def search_augment_data():
    """搜索Augment相关数据"""
    print("\n" + "=" * 60)
    print("🔍 搜索 Augment 相关数据")
    print("=" * 60)
    
    # 搜索storage.json
    storage_path = get_storage_path()
    try:
        with open(storage_path, 'r', encoding='utf-8') as f:
            storage_data = json.load(f)
        
        augment_keys = []
        for key, value in storage_data.items():
            if 'augment' in key.lower():
                augment_keys.append((key, value))
        
        if augment_keys:
            print(f"📁 Storage.json 中找到 {len(augment_keys)} 个 Augment 相关键:")
            for key, value in augment_keys:
                print(f"  - {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
        else:
            print("📁 Storage.json 中未找到 Augment 相关键")
            
    except Exception as e:
        print(f"❌ 搜索 storage.json 失败: {e}")
    
    # 搜索数据库
    db_path = get_db_path()
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%' OR value LIKE '%augment%'")
        rows = cursor.fetchall()
        
        if rows:
            print(f"\n🗃️ 数据库中找到 {len(rows)} 条 Augment 相关记录:")
            for key, value in rows:
                print(f"  - {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
        else:
            print(f"\n🗃️ 数据库中未找到 Augment 相关记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 搜索数据库失败: {e}")

def main():
    debug_storage()
    debug_database()
    search_augment_data()

if __name__ == "__main__":
    main()
