#!/usr/bin/env python3
"""
完整功能测试脚本
验证所有账号管理功能是否正常工作
"""

import os
import sys
from utils.account_manager import (
    save_login_info, 
    get_current_login_info, 
    clear_login_info,
    get_account_manager,
    format_account_status
)
from utils.account_detector import get_all_account_info, format_account_summary

def test_account_manager():
    """测试账号管理器功能"""
    print("🔧 测试账号管理器功能")
    print("-" * 50)
    
    # 测试账号管理器实例化
    manager = get_account_manager()
    print(f"✅ 账号管理器创建成功: {type(manager).__name__}")
    
    # 测试保存账号
    test_email = "<EMAIL>"
    test_username = "测试用户"
    test_cookie = "session=test123; user=testuser; auth=testauth"
    
    print(f"\n📝 测试保存账号: {test_email}")
    success = manager.save_account(test_email, test_cookie, test_username)
    print(f"保存结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 测试获取账号
    print(f"\n🔍 测试获取账号")
    account = manager.get_account(test_email)
    if account:
        print(f"✅ 获取成功:")
        print(f"   邮箱: {account.get('email')}")
        print(f"   用户名: {account.get('username')}")
        print(f"   创建时间: {account.get('created_time')}")
    else:
        print("❌ 获取失败")
    
    # 测试设置活跃账号
    print(f"\n⚡ 测试设置活跃账号")
    success = manager.set_active_account(test_email)
    print(f"设置结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 测试获取活跃账号
    active_account = manager.get_active_account()
    if active_account:
        print(f"✅ 活跃账号: {active_account.get('email')}")
    else:
        print("❌ 未找到活跃账号")
    
    # 测试账号汇总
    print(f"\n📊 测试账号汇总")
    summary = manager.get_account_summary()
    print(f"总账号数: {summary['total_accounts']}")
    print(f"有活跃账号: {summary['has_active_account']}")
    print(f"活跃邮箱: {summary['active_email']}")
    
    return True

def test_login_info_functions():
    """测试登录信息函数"""
    print("\n🔐 测试登录信息函数")
    print("-" * 50)
    
    # 清除现有登录信息
    print("🧹 清除现有登录信息")
    clear_result = clear_login_info()
    print(f"清除结果: {'✅ 成功' if clear_result else '❌ 失败'}")
    
    # 测试获取空登录信息
    print("\n🔍 测试获取空登录信息")
    login_info = get_current_login_info()
    print(f"登录信息: {'❌ 无' if login_info is None else '✅ 有'}")
    
    # 测试保存登录信息
    print("\n💾 测试保存登录信息")
    test_email = "<EMAIL>"
    test_username = "演示用户"
    test_cookie = "session=demo_session; user=demo_user; auth=demo_auth"
    
    save_result = save_login_info(test_email, test_cookie, test_username)
    print(f"保存结果: {'✅ 成功' if save_result else '❌ 失败'}")
    
    # 测试获取保存的登录信息
    print("\n🔍 测试获取保存的登录信息")
    login_info = get_current_login_info()
    if login_info:
        print(f"✅ 获取成功:")
        print(f"   邮箱: {login_info.get('email')}")
        print(f"   用户名: {login_info.get('username')}")
        print(f"   最后使用: {login_info.get('last_used')}")
        print(f"   Cookie长度: {len(login_info.get('cookie_string', ''))} 字符")
    else:
        print("❌ 获取失败")
    
    # 测试格式化账号状态
    print("\n📋 测试格式化账号状态")
    status = format_account_status(include_subscription=False)
    print("账号状态:")
    print(status)
    
    return True

def test_account_detection():
    """测试账号检测功能"""
    print("\n🔍 测试账号检测功能")
    print("-" * 50)
    
    # 测试获取所有账号信息
    print("📊 获取所有账号信息")
    account_info = get_all_account_info()
    
    # 检查返回的数据结构
    expected_keys = ['vscode_accounts', 'augment_account', 'augment_activity', 'workspace_info', 'summary']
    for key in expected_keys:
        if key in account_info:
            print(f"✅ {key}: 存在")
        else:
            print(f"❌ {key}: 缺失")
    
    # 检查AugmentCode账号信息
    augment_account = account_info.get('augment_account', {})
    print(f"\n🎯 AugmentCode账号检测:")
    print(f"   登录状态: {'✅ 已登录' if augment_account.get('logged_in') else '❌ 未登录'}")
    if augment_account.get('logged_in'):
        print(f"   邮箱: {augment_account.get('email', '未知')}")
        print(f"   用户名: {augment_account.get('username', '未知')}")
        print(f"   登录方式: {augment_account.get('login_method', '未知')}")
    
    # 检查活动状态
    augment_activity = account_info.get('augment_activity', {})
    print(f"\n🚀 AugmentCode活动状态:")
    print(f"   活跃状态: {'✅ 活跃' if augment_activity.get('is_active') else '❌ 不活跃'}")
    print(f"   扩展确认: {'✅ 是' if augment_activity.get('extension_confirmed') else '❌ 否'}")
    print(f"   发布者信任: {'✅ 是' if augment_activity.get('publisher_trusted') else '❌ 否'}")
    
    # 测试格式化摘要
    print(f"\n📋 测试格式化摘要:")
    summary = format_account_summary(account_info)
    print(summary)
    
    return True

def test_gui_compatibility():
    """测试GUI兼容性"""
    print("\n🖥️ 测试GUI兼容性")
    print("-" * 50)
    
    try:
        # 测试导入GUI模块
        from gui.account_view import AccountView
        print("✅ AccountView 导入成功")
        
        # 测试导入其他必要模块
        import customtkinter as ctk
        print("✅ customtkinter 导入成功")
        
        import tkinter.messagebox as messagebox
        print("✅ messagebox 导入成功")
        
        print("✅ GUI兼容性测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ GUI兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 AugmentCode 完整功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    try:
        result1 = test_account_manager()
        test_results.append(("账号管理器", result1))
    except Exception as e:
        print(f"❌ 账号管理器测试失败: {e}")
        test_results.append(("账号管理器", False))
    
    try:
        result2 = test_login_info_functions()
        test_results.append(("登录信息函数", result2))
    except Exception as e:
        print(f"❌ 登录信息函数测试失败: {e}")
        test_results.append(("登录信息函数", False))
    
    try:
        result3 = test_account_detection()
        test_results.append(("账号检测", result3))
    except Exception as e:
        print(f"❌ 账号检测测试失败: {e}")
        test_results.append(("账号检测", False))
    
    try:
        result4 = test_gui_compatibility()
        test_results.append(("GUI兼容性", result4))
    except Exception as e:
        print(f"❌ GUI兼容性测试失败: {e}")
        test_results.append(("GUI兼容性", False))
    
    # 显示测试结果汇总
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总")
    print("-" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！功能完整可用。")
        print("\n💡 使用建议:")
        print("1. 运行 'python main.py' 启动GUI")
        print("2. 进入'账号管理'页面输入您的登录信息")
        print("3. 点击'获取帮助'查看Cookie获取指南")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
