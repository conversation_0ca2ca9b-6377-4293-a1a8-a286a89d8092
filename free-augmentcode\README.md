# AugmentCode管理工具

一个用于管理AugmentCode账号和相关配置的现代化图形界面工具。

## 功能特点

- **仪表盘**: 显示系统状态概览，包括账号状态、系统检测结果、工作区状态和活动状态
- **系统检测**: 检测系统中的AugmentCode相关配置
- **工作区清理**: 清理VS Code工作区中的相关文件和配置
- **账号修改**: 管理AugmentCode账号设置

## 安装和使用

1. 确保已安装Python 3.8或更高版本
2. 安装所需依赖：
   ```bash
   pip install customtkinter pillow
   ```
3. 运行程序：
   ```bash
   python main.py
   ```

## 系统需求

- Python 3.8+
- customtkinter库
- Pillow库

## 界面预览

应用程序采用了现代化的界面设计，包括：
- 深色主题
- 左侧导航栏
- 卡片式内容布局
- 直观的用户操作界面

## 隐私说明

该工具仅用于本地管理您的AugmentCode配置，所有操作均在本地完成，不会上传或共享您的任何账号信息。 