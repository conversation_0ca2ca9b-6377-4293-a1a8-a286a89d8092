#!/usr/bin/env python3
"""
安装增强功能所需的依赖
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}... {description}")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def install_selenium_driver():
    """安装Selenium WebDriver"""
    try:
        print("🚗 安装 Chrome WebDriver...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "webdriver-manager"])
        print("✅ WebDriver Manager 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ WebDriver Manager 安装失败: {e}")
        return False

def main():
    """主安装函数"""
    print("🎯 安装增强账号信息获取功能的依赖")
    print("=" * 60)
    
    # 基础依赖
    basic_packages = [
        ("requests", "HTTP请求库"),
        ("beautifulsoup4", "HTML解析库"),
        ("lxml", "XML/HTML解析器"),
        ("selenium", "浏览器自动化库"),
        ("webdriver-manager", "WebDriver管理器"),
    ]
    
    # 可选依赖
    optional_packages = [
        ("fake-useragent", "随机User-Agent生成器"),
        ("undetected-chromedriver", "反检测Chrome驱动"),
        ("playwright", "现代浏览器自动化"),
        ("httpx", "异步HTTP客户端"),
    ]
    
    print("📦 安装基础依赖...")
    print("-" * 30)
    
    success_count = 0
    total_count = len(basic_packages)
    
    for package, description in basic_packages:
        if install_package(package, description):
            success_count += 1
    
    print(f"\n📊 基础依赖安装结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("✅ 所有基础依赖安装成功！")
    else:
        print("⚠️ 部分基础依赖安装失败，可能影响功能")
    
    print("\n📦 安装可选依赖...")
    print("-" * 30)
    
    optional_success = 0
    for package, description in optional_packages:
        if install_package(package, description):
            optional_success += 1
    
    print(f"\n📊 可选依赖安装结果: {optional_success}/{len(optional_packages)} 成功")
    
    print("\n" + "=" * 60)
    print("🎉 依赖安装完成！")
    
    print("\n💡 功能说明:")
    print("• 方法1: 直接API调用 - 尝试各种可能的API端点")
    print("• 方法2: 增强页面抓取 - 使用多种解析技术")
    print("• 方法3: 浏览器自动化 - 模拟真实浏览器行为")
    print("• 方法4: 网络流量分析 - 分析隐藏的API端点")
    
    print("\n🔧 使用方法:")
    print("from enhanced_account_methods import get_enhanced_account_info")
    print("success, data = get_enhanced_account_info(cookie_string)")
    
    if success_count < total_count:
        print("\n⚠️ 注意事项:")
        print("• 如果Selenium安装失败，浏览器自动化功能将不可用")
        print("• 可以手动安装失败的包: pip install <package_name>")
        print("• 确保系统已安装Chrome浏览器")

if __name__ == "__main__":
    main()
