#!/usr/bin/env python3
"""
Cookie验证和刷新工具
"""

import sys
import os
import requests
import json
import time
from typing import Dict, Optional, Tuple

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

class CookieValidator:
    """Cookie验证器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://app.augmentcode.com"
        
    def validate_cookie(self, cookie_string: str) -> Tuple[bool, Dict]:
        """
        验证Cookie是否有效
        
        Args:
            cookie_string: Cookie字符串
            
        Returns:
            tuple: (是否有效, 验证信息)
        """
        result = {
            'valid': False,
            'status': '',
            'redirect_url': '',
            'response_code': 0,
            'error': ''
        }
        
        try:
            # 解析Cookie
            cookies = self._parse_cookie_string(cookie_string)
            if not cookies:
                result['error'] = 'Cookie解析失败'
                return False, result
            
            # 设置会话
            self._setup_session(cookies)
            
            # 测试访问受保护的页面
            test_urls = [
                '/account/subscription',
                '/account',
                '/dashboard'
            ]
            
            for test_url in test_urls:
                try:
                    url = f"{self.base_url}{test_url}"
                    response = self.session.get(url, timeout=10, allow_redirects=False)
                    
                    result['response_code'] = response.status_code
                    
                    if response.status_code == 200:
                        # 检查是否真的是目标页面
                        if self._is_authenticated_page(response.text):
                            result['valid'] = True
                            result['status'] = '有效'
                            return True, result
                        else:
                            result['status'] = '页面内容表明未认证'
                    elif response.status_code in [301, 302, 303, 307, 308]:
                        # 重定向
                        redirect_url = response.headers.get('Location', '')
                        result['redirect_url'] = redirect_url
                        
                        if 'login' in redirect_url.lower() or 'signin' in redirect_url.lower():
                            result['status'] = '重定向到登录页面'
                        else:
                            result['status'] = f'重定向到: {redirect_url}'
                    elif response.status_code == 401:
                        result['status'] = '认证失败'
                    elif response.status_code == 403:
                        result['status'] = '访问被拒绝'
                    else:
                        result['status'] = f'HTTP {response.status_code}'
                        
                except requests.exceptions.Timeout:
                    result['error'] = '请求超时'
                except requests.exceptions.ConnectionError:
                    result['error'] = '连接错误'
                    
        except Exception as e:
            result['error'] = f'验证异常: {str(e)}'
        
        return False, result
    
    def refresh_cookie_from_browser(self) -> Tuple[bool, str]:
        """
        尝试从浏览器获取新的Cookie
        """
        try:
            # 尝试从VS Code扩展获取Cookie
            from utils.account_detector import get_all_account_info
            
            account_info = get_all_account_info()
            augment_account = account_info.get('augment_account', {})
            
            if augment_account.get('logged_in'):
                cookie_info = augment_account.get('cookie_info', {})
                if cookie_info.get('has_cookie'):
                    new_cookie = cookie_info.get('cookie_string', '')
                    if new_cookie:
                        return True, new_cookie
            
            return False, '未能从浏览器获取Cookie'
            
        except Exception as e:
            return False, f'从浏览器获取Cookie失败: {str(e)}'
    
    def get_cookie_suggestions(self, cookie_string: str) -> Dict:
        """
        获取Cookie问题的建议
        """
        suggestions = {
            'issues': [],
            'solutions': [],
            'next_steps': []
        }
        
        # 分析Cookie格式
        if not cookie_string:
            suggestions['issues'].append('Cookie字符串为空')
            suggestions['solutions'].append('重新登录AugmentCode获取Cookie')
        elif len(cookie_string) < 50:
            suggestions['issues'].append('Cookie字符串过短，可能不完整')
            suggestions['solutions'].append('确保复制了完整的Cookie字符串')
        elif '=' not in cookie_string:
            suggestions['issues'].append('Cookie格式不正确，缺少键值对')
            suggestions['solutions'].append('检查Cookie格式，应该包含key=value形式')
        
        # 验证Cookie
        valid, validation_info = self.validate_cookie(cookie_string)
        
        if not valid:
            suggestions['issues'].append(f"Cookie验证失败: {validation_info.get('status', '未知')}")
            
            if 'login' in validation_info.get('redirect_url', '').lower():
                suggestions['solutions'].append('Cookie已过期，需要重新登录')
                suggestions['next_steps'].append('1. 访问 https://app.augmentcode.com')
                suggestions['next_steps'].append('2. 重新登录账号')
                suggestions['next_steps'].append('3. 复制新的Cookie')
            elif validation_info.get('response_code') == 403:
                suggestions['solutions'].append('账号可能被限制访问')
                suggestions['next_steps'].append('检查账号状态')
            elif 'timeout' in validation_info.get('error', '').lower():
                suggestions['solutions'].append('网络连接问题')
                suggestions['next_steps'].append('检查网络连接')
        else:
            suggestions['solutions'].append('Cookie有效，可能是其他问题')
        
        return suggestions
    
    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        
        try:
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for part in parts:
                    if '=' in part:
                        key, value = part.strip().split('=', 1)
                        cookies[key.strip()] = value.strip()
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()
            else:
                # 尝试常见的Cookie名称
                common_names = ['auth_token', 'access_token', 'session', 'token', 'jwt', '_session']
                for name in common_names:
                    cookies[name] = cookie_string.strip()
        except:
            pass
            
        return cookies
    
    def _setup_session(self, cookies: Dict[str, str]):
        """设置会话"""
        for key, value in cookies.items():
            self.session.cookies.set(key, value)
        
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
    
    def _is_authenticated_page(self, html: str) -> bool:
        """检查页面是否为已认证状态"""
        # 检查是否包含登录相关的内容
        login_indicators = ['login', 'signin', 'sign in', 'log in', 'authentication']
        logout_indicators = ['logout', 'signout', 'sign out', 'log out']
        auth_indicators = ['subscription', 'account', 'dashboard', 'profile', 'billing']
        
        html_lower = html.lower()
        
        # 如果包含登出按钮，通常表示已登录
        if any(indicator in html_lower for indicator in logout_indicators):
            return True
        
        # 如果包含认证相关内容且不包含登录表单，可能已认证
        has_auth_content = any(indicator in html_lower for indicator in auth_indicators)
        has_login_form = any(indicator in html_lower for indicator in login_indicators)
        
        return has_auth_content and not has_login_form


def validate_current_cookie():
    """验证当前保存的Cookie"""
    print("🔍 验证当前保存的Cookie")
    print("=" * 40)
    
    try:
        from utils.account_manager import get_current_login_info
        
        current_info = get_current_login_info()
        if not current_info:
            print("❌ 未找到保存的登录信息")
            return
        
        email = current_info['email']
        cookie_string = current_info.get('cookie_string', '')
        
        print(f"👤 账号: {email}")
        print(f"🍪 Cookie长度: {len(cookie_string)} 字符")
        
        if not cookie_string:
            print("❌ Cookie字符串为空")
            return
        
        # 验证Cookie
        validator = CookieValidator()
        valid, validation_info = validator.validate_cookie(cookie_string)
        
        print(f"\n🔍 验证结果:")
        print(f"✅ 有效性: {'有效' if valid else '无效'}")
        print(f"📊 状态: {validation_info.get('status', '未知')}")
        print(f"🔢 响应码: {validation_info.get('response_code', 0)}")
        
        if validation_info.get('redirect_url'):
            print(f"🔄 重定向: {validation_info['redirect_url']}")
        
        if validation_info.get('error'):
            print(f"❌ 错误: {validation_info['error']}")
        
        # 获取建议
        suggestions = validator.get_cookie_suggestions(cookie_string)
        
        if suggestions['issues']:
            print(f"\n⚠️ 发现的问题:")
            for issue in suggestions['issues']:
                print(f"  • {issue}")
        
        if suggestions['solutions']:
            print(f"\n💡 建议的解决方案:")
            for solution in suggestions['solutions']:
                print(f"  • {solution}")
        
        if suggestions['next_steps']:
            print(f"\n🔧 下一步操作:")
            for step in suggestions['next_steps']:
                print(f"  {step}")
        
        # 尝试刷新Cookie
        if not valid:
            print(f"\n🔄 尝试从浏览器刷新Cookie...")
            refresh_success, new_cookie = validator.refresh_cookie_from_browser()
            
            if refresh_success:
                print("✅ 成功从浏览器获取新Cookie")
                print(f"🍪 新Cookie长度: {len(new_cookie)} 字符")
                
                # 验证新Cookie
                new_valid, new_validation = validator.validate_cookie(new_cookie)
                print(f"🔍 新Cookie验证: {'有效' if new_valid else '无效'}")
                
                if new_valid:
                    print("💡 建议更新保存的Cookie")
            else:
                print(f"❌ 刷新失败: {new_cookie}")
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")


def main():
    """主函数"""
    print("🔍 Cookie验证和刷新工具")
    print("=" * 50)
    
    try:
        validate_current_cookie()
        
        print("\n" + "=" * 50)
        print("🎯 验证完成！")
        
        print("\n💡 Cookie问题解决方案:")
        print("1. 重新登录AugmentCode网站")
        print("2. 使用浏览器开发者工具复制Cookie")
        print("3. 确保复制完整的Cookie字符串")
        print("4. 检查网络连接是否正常")
        print("5. 确认账号状态是否正常")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
