#!/usr/bin/env python3
"""
账号管理模块
管理用户输入的账号和Cookie信息
"""

import json
import os
import time
from typing import Dict, Optional, List, Tuple
from datetime import datetime
from .paths import get_app_data_dir

class AccountManager:
    """账号管理器"""
    
    def __init__(self):
        self.config_dir = get_app_data_dir()
        self.config_file = os.path.join(self.config_dir, "augment_accounts.json")
        self.ensure_config_dir()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir, exist_ok=True)
    
    def save_account(self, email: str, cookie_string: str, username: str = "") -> bool:
        """
        保存账号信息
        
        Args:
            email: 邮箱
            cookie_string: Cookie字符串
            username: 用户名（可选）
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 读取现有配置
            accounts = self.load_accounts()
            
            # 创建账号信息
            account_info = {
                'email': email,
                'username': username or email.split('@')[0],
                'cookie_string': cookie_string,
                'created_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'last_used': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'is_active': True
            }
            
            # 更新或添加账号
            accounts[email] = account_info
            
            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"保存账号失败: {e}")
            return False
    
    def load_accounts(self) -> Dict[str, Dict]:
        """
        加载所有账号信息
        
        Returns:
            dict: 账号信息字典
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载账号失败: {e}")
            return {}
    
    def get_account(self, email: str) -> Optional[Dict]:
        """
        获取指定账号信息
        
        Args:
            email: 邮箱
            
        Returns:
            dict: 账号信息，如果不存在返回None
        """
        accounts = self.load_accounts()
        return accounts.get(email)
    
    def get_active_account(self) -> Optional[Dict]:
        """
        获取当前活跃的账号
        
        Returns:
            dict: 活跃账号信息，如果没有返回None
        """
        accounts = self.load_accounts()
        for email, account in accounts.items():
            if account.get('is_active', False):
                return account
        return None
    
    def set_active_account(self, email: str) -> bool:
        """
        设置活跃账号
        
        Args:
            email: 邮箱
            
        Returns:
            bool: 是否设置成功
        """
        try:
            accounts = self.load_accounts()
            
            # 取消所有账号的活跃状态
            for acc_email, account in accounts.items():
                account['is_active'] = False
            
            # 设置指定账号为活跃
            if email in accounts:
                accounts[email]['is_active'] = True
                accounts[email]['last_used'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 保存配置
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(accounts, f, indent=2, ensure_ascii=False)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"设置活跃账号失败: {e}")
            return False
    
    def delete_account(self, email: str) -> bool:
        """
        删除账号
        
        Args:
            email: 邮箱
            
        Returns:
            bool: 是否删除成功
        """
        try:
            accounts = self.load_accounts()
            
            if email in accounts:
                del accounts[email]
                
                # 保存配置
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(accounts, f, indent=2, ensure_ascii=False)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"删除账号失败: {e}")
            return False
    
    def list_accounts(self) -> List[Dict]:
        """
        列出所有账号
        
        Returns:
            list: 账号信息列表
        """
        accounts = self.load_accounts()
        account_list = []
        
        for email, account in accounts.items():
            # 隐藏敏感的Cookie信息
            safe_account = account.copy()
            if 'cookie_string' in safe_account:
                cookie_len = len(safe_account['cookie_string'])
                safe_account['cookie_preview'] = f"Cookie ({cookie_len} 字符)"
                del safe_account['cookie_string']
            
            account_list.append(safe_account)
        
        return account_list
    
    def clear_all_accounts(self) -> bool:
        """
        清空所有账号
        
        Returns:
            bool: 是否清空成功
        """
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)
            return True
        except Exception as e:
            print(f"清空账号失败: {e}")
            return False
    
    def get_account_summary(self) -> Dict:
        """
        获取账号汇总信息
        
        Returns:
            dict: 账号汇总信息
        """
        accounts = self.load_accounts()
        active_account = self.get_active_account()
        
        return {
            'total_accounts': len(accounts),
            'has_active_account': active_account is not None,
            'active_email': active_account.get('email', '') if active_account else '',
            'active_username': active_account.get('username', '') if active_account else '',
            'last_login_time': active_account.get('last_used', '') if active_account else '',
            'config_file_exists': os.path.exists(self.config_file)
        }


def get_account_manager() -> AccountManager:
    """获取账号管理器实例"""
    return AccountManager()


def get_current_login_info() -> Optional[Dict]:
    """
    获取当前登录信息
    
    Returns:
        Dict or None: 登录信息，如果未登录则返回None
    """
    if not os.path.exists(os.path.join(get_app_data_dir(), "login_info.json")):
        return None
    
    try:
        with open(os.path.join(get_app_data_dir(), "login_info.json"), 'r', encoding='utf-8') as f:
            login_info = json.load(f)
        
        # 检查必要的字段
        if 'email' not in login_info or 'cookie_string' not in login_info:
            return None
            
        return login_info
    except (json.JSONDecodeError, OSError):
        return None


def save_login_info(email: str, cookie_string: str, username: str = "") -> bool:
    """
    保存登录信息
    
    Args:
        email: 登录邮箱
        cookie_string: Cookie字符串
        username: 用户名，可选
        
    Returns:
        bool: 保存成功返回True，否则返回False
    """
    # 确保配置目录存在
    get_account_manager().ensure_config_dir()
    
    # 检查参数有效性
    if not email or not cookie_string:
        return False
    
    # 准备登录信息
    login_info = {
        'email': email,
        'username': username or email.split('@')[0],
        'cookie_string': cookie_string,
        'last_used': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'timestamp': int(time.time())
    }
    
    # 保存登录信息
    try:
        with open(os.path.join(get_app_data_dir(), "login_info.json"), 'w', encoding='utf-8') as f:
            json.dump(login_info, f, indent=4)
        return True
    except OSError:
        return False


def update_cookie(cookie_string: str) -> bool:
    """
    更新Cookie
    
    Args:
        cookie_string: 新的Cookie字符串
        
    Returns:
        bool: 更新成功返回True，否则返回False
    """
    # 获取当前登录信息
    login_info = get_current_login_info()
    if not login_info:
        return False
    
    # 更新Cookie
    return save_login_info(
        login_info['email'],
        cookie_string,
        login_info.get('username', '')
    )


def clear_login_info() -> bool:
    """
    清除登录信息
    
    Returns:
        bool: 清除成功返回True，否则返回False
    """
    if os.path.exists(os.path.join(get_app_data_dir(), "login_info.json")):
        try:
            os.remove(os.path.join(get_app_data_dir(), "login_info.json"))
            return True
        except OSError:
            return False
    return True


def validate_cookie(cookie_string: str) -> Dict:
    """
    验证Cookie是否有效
    
    Args:
        cookie_string: Cookie字符串
        
    Returns:
        dict: 包含验证结果的字典
    """
    # 在实际应用中，这里应该发送请求到服务器验证Cookie
    # 此处仅做简单格式检查
    result = {
        'valid': False,
        'message': '',
        'user_info': None
    }
    
    if not cookie_string:
        result['message'] = '无效的Cookie：为空'
        return result
        
    # 检查Cookie格式 (简单检查)
    required_fields = ['session', 'user']
    has_fields = all(field in cookie_string.lower() for field in required_fields)
    
    if not has_fields:
        result['message'] = '无效的Cookie：缺少必要字段'
        return result
    
    # 在实际应用中，这里会通过网络请求验证Cookie的有效性
    # 由于是演示版本，我们只是简单地判定Cookie有效
    result['valid'] = True
    result['message'] = 'Cookie验证成功'
    result['user_info'] = {
        'email': '用户邮箱将从服务器获取',
        'username': '用户名将从服务器获取'
    }
    
    return result


def get_account_subscription_info(cookie_string: str = None) -> Tuple[bool, Dict]:
    """
    获取账号订阅信息

    Args:
        cookie_string: Cookie字符串，如果为None则使用当前登录的Cookie

    Returns:
        tuple: (是否成功, 订阅信息)
    """
    try:
        # 如果没有提供Cookie，尝试从当前登录信息获取
        if not cookie_string:
            current_info = get_current_login_info()
            if not current_info:
                return False, {'error': '未找到登录信息'}
            cookie_string = current_info.get('cookie_string')
            if not cookie_string:
                return False, {'error': '未找到Cookie信息'}

        # 导入订阅检查器
        from .subscription_checker import quick_check_subscription

        # 检查订阅信息
        success, sub_info = quick_check_subscription(cookie_string)
        return success, sub_info

    except Exception as e:
        return False, {'error': f'获取订阅信息失败: {str(e)}'}


def format_account_status(include_subscription: bool = True) -> str:
    """
    格式化账号状态信息

    Args:
        include_subscription: 是否包含订阅信息

    Returns:
        str: 格式化的状态信息
    """
    # 获取当前登录信息
    login_info = get_current_login_info()
    if not login_info:
        return "❌ 未登录"

    lines = []
    lines.append(f"✅ 已登录: {login_info['email']}")
    lines.append(f"👤 用户名: {login_info['username']}")
    lines.append(f"🕒 最后使用: {login_info['last_used']}")

    # 如果需要包含订阅信息
    if include_subscription:
        success, sub_info = get_account_subscription_info()
        if success:
            lines.append("")  # 空行分隔
            lines.append("📊 订阅信息:")

            if sub_info.get('plan_name'):
                lines.append(f"  📋 计划: {sub_info['plan_name']}")

            if sub_info.get('usage_limit', 0) > 0:
                usage_count = sub_info.get('usage_count', 0)
                usage_limit = sub_info.get('usage_limit', 0)
                remaining = sub_info.get('remaining_count', 0)
                usage_percent = (usage_count / usage_limit) * 100 if usage_limit > 0 else 0

                lines.append(f"  🔢 使用情况: {usage_count}/{usage_limit} ({usage_percent:.1f}%)")
                lines.append(f"  ⚡ 剩余次数: {remaining} 次")
            else:
                lines.append(f"  🔢 已使用: {sub_info.get('usage_count', 0)} 次")

            if sub_info.get('reset_date'):
                lines.append(f"  🔄 重置日期: {sub_info['reset_date']}")
        else:
            lines.append("")
            lines.append(f"⚠️ 订阅信息: {sub_info.get('error', '获取失败')}")

    return "\n".join(lines)
