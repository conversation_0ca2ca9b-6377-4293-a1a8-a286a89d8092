#!/usr/bin/env python3
"""
简化版GUI测试 - 用于测试按钮显示问题
"""

import customtkinter as ctk
from tkinter import messagebox

# 设置主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class TestGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("测试GUI - 按钮显示")
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ctk.CTkLabel(
            self.root, 
            text="🚀 Free AugmentCode 测试", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 测试信息
        info_label = ctk.CTkLabel(
            main_frame,
            text="这是一个简化的测试界面，用于验证按钮是否正常显示",
            font=ctk.CTkFont(size=14)
        )
        info_label.pack(pady=20)
        
        # 警告提示
        warning_frame = ctk.CTkFrame(main_frame)
        warning_frame.pack(fill="x", padx=20, pady=10)
        
        warning_label = ctk.CTkLabel(
            warning_frame,
            text="⚠️ 使用前请确保：\n1. 完全退出 VS Code\n2. 退出 AugmentCode 插件",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        )
        warning_label.pack(pady=10)
        
        # 主要操作按钮
        self.start_button = ctk.CTkButton(
            main_frame,
            text="🚀 开始清理 AugmentCode 数据",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            command=self.test_function
        )
        self.start_button.pack(fill="x", padx=20, pady=20)
        
        # 其他测试按钮
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        test_button1 = ctk.CTkButton(
            button_frame,
            text="测试按钮 1",
            command=lambda: messagebox.showinfo("测试", "按钮1被点击")
        )
        test_button1.pack(side="left", padx=10, pady=10)
        
        test_button2 = ctk.CTkButton(
            button_frame,
            text="测试按钮 2",
            command=lambda: messagebox.showinfo("测试", "按钮2被点击")
        )
        test_button2.pack(side="left", padx=10, pady=10)
        
        test_button3 = ctk.CTkButton(
            button_frame,
            text="退出",
            command=self.root.quit
        )
        test_button3.pack(side="right", padx=10, pady=10)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="状态：准备就绪",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=10)
        
    def test_function(self):
        """测试功能"""
        result = messagebox.askyesno(
            "确认测试", 
            "这是一个测试功能。\n\n确定要继续吗？"
        )
        
        if result:
            self.status_label.configure(text="状态：测试功能已执行")
            messagebox.showinfo("完成", "测试功能执行完成！")
        else:
            self.status_label.configure(text="状态：测试已取消")
        
    def run(self):
        """运行GUI应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = TestGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"GUI 启动失败：{str(e)}")

if __name__ == "__main__":
    main()
