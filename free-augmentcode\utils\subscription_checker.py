#!/usr/bin/env python3
"""
订阅信息检查模块
查询AugmentCode账号的使用次数和订阅状态
"""

import requests
import json
from typing import Dict, Optional, Tuple
from datetime import datetime
import urllib.parse

class SubscriptionChecker:
    """订阅信息检查器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://app.augmentcode.com"
        
    def setup_session(self, cookies: Dict[str, str]):
        """
        设置会话Cookie
        
        Args:
            cookies: Cookie字典
        """
        for key, value in cookies.items():
            self.session.cookies.set(key, value)
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Referer': 'https://app.augmentcode.com/',
            'Origin': 'https://app.augmentcode.com'
        })
    
    def get_subscription_info(self, cookie_string: str) -> Dict:
        """
        获取订阅信息
        
        Args:
            cookie_string: Cookie字符串
            
        Returns:
            dict: 订阅信息
        """
        result = {
            'success': False,
            'plan_name': '',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '',
            'subscription_status': '',
            'error': ''
        }
        
        try:
            # 解析Cookie
            cookies = self._parse_cookie_string(cookie_string)
            if not cookies:
                result['error'] = 'Cookie解析失败'
                return result
            
            # 设置会话
            self.setup_session(cookies)
            
            # 尝试获取订阅信息
            subscription_data = self._fetch_subscription_data()
            if subscription_data:
                result.update(subscription_data)
                result['success'] = True
            else:
                # 如果API调用失败，尝试从页面解析
                page_data = self._fetch_from_page()
                if page_data:
                    result.update(page_data)
                    result['success'] = True
                else:
                    result['error'] = '无法获取订阅信息'
            
        except Exception as e:
            result['error'] = f'查询过程出错: {str(e)}'
        
        return result
    
    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        
        try:
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for part in parts:
                    if '=' in part:
                        key, value = part.strip().split('=', 1)
                        cookies[key.strip()] = value.strip()
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()
            else:
                # 如果是单个token，尝试不同的key名
                cookies['auth_token'] = cookie_string.strip()
                cookies['access_token'] = cookie_string.strip()
                cookies['session'] = cookie_string.strip()
        except:
            pass
            
        return cookies
    
    def _fetch_subscription_data(self) -> Optional[Dict]:
        """从API获取订阅数据"""
        try:
            # 尝试访问订阅API端点
            api_endpoints = [
                '/api/account/subscription',
                '/api/user/subscription',
                '/api/subscription',
                '/account/subscription'
            ]
            
            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        return self._parse_api_response(data)
                        
                except (requests.RequestException, json.JSONDecodeError):
                    continue
                    
        except Exception:
            pass
            
        return None
    
    def _fetch_from_page(self) -> Optional[Dict]:
        """从页面HTML解析订阅信息"""
        try:
            # 访问订阅页面
            url = f"{self.base_url}/account/subscription"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                return self._parse_html_response(response.text)
                
        except Exception:
            pass
            
        return None
    
    def _parse_api_response(self, data: Dict) -> Dict:
        """解析API响应"""
        result = {}
        
        try:
            # 尝试解析不同的API响应格式
            if 'subscription' in data:
                sub_data = data['subscription']
            elif 'plan' in data:
                sub_data = data
            else:
                sub_data = data
            
            # 提取订阅信息
            result['plan_name'] = sub_data.get('plan_name', sub_data.get('plan', 'Unknown'))
            result['usage_count'] = sub_data.get('usage_count', sub_data.get('used', 0))
            result['usage_limit'] = sub_data.get('usage_limit', sub_data.get('limit', 0))
            result['remaining_count'] = result['usage_limit'] - result['usage_count']
            result['reset_date'] = sub_data.get('reset_date', sub_data.get('next_reset', ''))
            result['subscription_status'] = sub_data.get('status', 'active')
            
        except Exception:
            # 如果解析失败，返回默认值
            result = {
                'plan_name': 'Cookie登录',
                'usage_count': 0,
                'usage_limit': 100,  # 假设默认限制
                'remaining_count': 100,
                'reset_date': '未知',
                'subscription_status': 'active'
            }
        
        return result
    
    def _parse_html_response(self, html: str) -> Dict:
        """解析HTML页面"""
        result = {
            'plan_name': 'Cookie登录',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '未知',
            'subscription_status': 'active'
        }

        try:
            import re

            # 查找AugmentCode特定的订阅信息格式

            # 1. 查找剩余次数 "27.00 available" 或 "27 available"
            available_pattern = r'(\d+(?:\.\d+)?)\s+available'
            available_match = re.search(available_pattern, html, re.IGNORECASE)
            if available_match:
                # 转换为整数（如果是整数值）
                remaining_value = float(available_match.group(1))
                result['remaining_count'] = int(remaining_value) if remaining_value.is_integer() else remaining_value

            # 2. 查找使用情况 "Used 23 of 50 this month"
            used_pattern = r'Used\s+(\d+)\s+of\s+(\d+)\s+this\s+month'
            used_match = re.search(used_pattern, html, re.IGNORECASE)
            if used_match:
                result['usage_count'] = int(used_match.group(1))
                result['usage_limit'] = int(used_match.group(2))
                # 如果没有找到available，从这里计算剩余次数
                if result['remaining_count'] == 0:
                    result['remaining_count'] = result['usage_limit'] - result['usage_count']

            # 3. 查找计划名称 "Community Plan", "Pro Plan" 等
            plan_patterns = [
                r'(Community\s+Plan)',
                r'(Pro\s+Plan)',
                r'(Enterprise\s+Plan)',
                r'(Free\s+Plan)',
                r'(Basic\s+Plan)',
                r'Current\s+plan[^>]*>([^<]+)',
                r'plan["\']?\s*:\s*["\']([^"\']+)["\']'
            ]

            for pattern in plan_patterns:
                plan_match = re.search(pattern, html, re.IGNORECASE)
                if plan_match:
                    result['plan_name'] = plan_match.group(1).strip()
                    break

            # 4. 查找下次计费日期 "July 11, 2025"
            date_patterns = [
                # 匹配 "July 11, 2025" 格式的日期
                r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d+,\s+\d{4}',
                # 匹配HTML标签中的日期
                r'Next\s+Billing\s+Date[^>]*>([^<]+)',
                r'<div[^>]*next-billing[^>]*>([^<]+)',
                r'<div[^>]*billing-label[^>]*>Next\s+Billing\s+Date</div>[^<]*<div[^>]*>([^<]+)',
                # 匹配ISO格式日期
                r'(\d{4}-\d{2}-\d{2})',
                # 匹配JSON格式
                r'reset["\']?\s*:\s*["\']([^"\']+)["\']'
            ]

            for pattern in date_patterns:
                date_match = re.search(pattern, html, re.IGNORECASE)
                if date_match:
                    # 对于第一个模式，直接使用整个匹配
                    if pattern == date_patterns[0]:
                        result['reset_date'] = date_match.group(0).strip()
                    else:
                        result['reset_date'] = date_match.group(1).strip()
                    break

            # 5. 查找月度续费信息 "50 renew monthly"
            renew_pattern = r'(\d+)\s+renew\s+monthly'
            renew_match = re.search(renew_pattern, html, re.IGNORECASE)
            if renew_match and result['usage_limit'] == 0:
                result['usage_limit'] = int(renew_match.group(1))
                # 重新计算剩余次数
                if result['usage_count'] > 0:
                    result['remaining_count'] = result['usage_limit'] - result['usage_count']

            # 6. 备用模式：查找数字模式
            if result['usage_count'] == 0 and result['usage_limit'] == 0:
                # 尝试查找其他数字模式
                number_patterns = [
                    r'(\d+)\s*/\s*(\d+)\s*(?:messages?|requests?|uses?)',
                    r'(\d+)\s+(?:of|/)\s+(\d+)',
                    r'usage["\']?\s*:\s*(\d+)',
                    r'limit["\']?\s*:\s*(\d+)'
                ]

                for pattern in number_patterns:
                    matches = re.findall(pattern, html, re.IGNORECASE)
                    if matches:
                        if len(matches[0]) == 2:  # 格式如 "23/50"
                            result['usage_count'] = int(matches[0][0])
                            result['usage_limit'] = int(matches[0][1])
                            result['remaining_count'] = result['usage_limit'] - result['usage_count']
                        break

            # 确保数据一致性
            if result['usage_limit'] > 0 and result['remaining_count'] == 0:
                result['remaining_count'] = max(0, result['usage_limit'] - result['usage_count'])

            # 如果找到了有效数据，更新计划名称
            if result['usage_limit'] > 0 or result['remaining_count'] > 0:
                if result['plan_name'] == 'Cookie登录':
                    result['plan_name'] = 'AugmentCode Plan'

        except Exception as e:
            # 记录解析错误但不影响返回
            result['parse_error'] = str(e)

        return result
    
    def format_subscription_info(self, sub_info: Dict) -> str:
        """格式化订阅信息为可读字符串"""
        if not sub_info.get('success'):
            return f"❌ 获取订阅信息失败: {sub_info.get('error', '未知错误')}"
        
        lines = []
        lines.append(f"📋 订阅计划: {sub_info['plan_name']}")
        
        if sub_info['usage_limit'] > 0:
            usage_percent = (sub_info['usage_count'] / sub_info['usage_limit']) * 100
            lines.append(f"📊 使用情况: {sub_info['usage_count']}/{sub_info['usage_limit']} ({usage_percent:.1f}%)")
            lines.append(f"🔢 剩余次数: {sub_info['remaining_count']} 次")
        else:
            lines.append(f"📊 已使用: {sub_info['usage_count']} 次")
        
        if sub_info['reset_date']:
            lines.append(f"🔄 重置日期: {sub_info['reset_date']}")
        
        lines.append(f"✅ 状态: {sub_info['subscription_status']}")
        
        return "\n".join(lines)


def get_subscription_checker() -> SubscriptionChecker:
    """获取订阅检查器实例"""
    return SubscriptionChecker()


def quick_check_subscription(cookie_string: str) -> Tuple[bool, Dict]:
    """
    快速检查订阅信息

    Args:
        cookie_string: Cookie字符串

    Returns:
        tuple: (是否成功, 订阅信息)
    """
    # 首先尝试原有方法
    checker = get_subscription_checker()
    result = checker.get_subscription_info(cookie_string)

    if result.get('success', False):
        return True, result

    # 如果原有方法失败，尝试增强方法
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))

        from enhanced_account_methods import get_enhanced_account_info

        print("🔄 原有方法失败，尝试增强方法...")
        enhanced_success, enhanced_data = get_enhanced_account_info(cookie_string)

        if enhanced_success:
            print("✅ 增强方法成功获取数据")
            # 转换数据格式以保持兼容性
            enhanced_data['success'] = True
            return True, enhanced_data
        else:
            print("❌ 增强方法也失败了")

    except ImportError:
        print("⚠️ 增强方法模块未找到，请运行 install_enhanced_dependencies.py")
    except Exception as e:
        print(f"⚠️ 增强方法异常: {e}")

    # 如果所有方法都失败，返回原始结果
    return result.get('success', False), result
