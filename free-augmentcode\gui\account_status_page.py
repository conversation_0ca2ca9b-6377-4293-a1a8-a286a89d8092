#!/usr/bin/env python3
"""
账号状态页面
集中显示所有账号相关信息，包括登录状态、订阅信息、使用情况等
"""

import customtkinter as ctk
from tkinter import messagebox
import threading
from datetime import datetime
from typing import Optional, Dict

# 导入相关模块
from utils.account_manager import (
    get_current_login_info, get_account_subscription_info,
    format_account_status, get_account_manager
)
from utils.cookie_login import quick_validate_login
from utils.account_detector import get_all_account_info, detect_augment_activity


class AccountStatusPage:
    """账号状态页面类"""
    
    def __init__(self, parent_frame, log_callback=None):
        """
        初始化账号状态页面
        
        Args:
            parent_frame: 父容器框架
            log_callback: 日志回调函数
        """
        self.parent_frame = parent_frame
        self.log_callback = log_callback or self._default_log
        self.refresh_in_progress = False
        
        # 创建主容器
        self.main_container = ctk.CTkScrollableFrame(parent_frame)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 初始化界面
        self.setup_ui()
        
        # 自动刷新账号信息
        self.refresh_account_status()
    
    def _default_log(self, message, level="INFO"):
        """默认日志函数"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_container,
            text="👤 账号状态中心",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # 创建各个状态区域
        self.create_login_status_section()
        self.create_subscription_section()
        self.create_system_detection_section()
        self.create_activity_section()
        self.create_action_buttons_section()
    
    def create_login_status_section(self):
        """创建登录状态区域"""
        # 登录状态框架
        self.login_frame = ctk.CTkFrame(self.main_container)
        self.login_frame.pack(fill="x", padx=10, pady=(0, 15))
        
        # 区域标题
        login_title = ctk.CTkLabel(
            self.login_frame,
            text="🔐 登录状态",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        login_title.pack(pady=(15, 10))
        
        # 登录状态内容区域
        self.login_content_frame = ctk.CTkFrame(self.login_frame)
        self.login_content_frame.pack(fill="x", padx=15, pady=(0, 15))
    
    def create_subscription_section(self):
        """创建订阅信息区域"""
        # 订阅信息框架
        self.subscription_frame = ctk.CTkFrame(self.main_container)
        self.subscription_frame.pack(fill="x", padx=10, pady=(0, 15))
        
        # 区域标题
        sub_title = ctk.CTkLabel(
            self.subscription_frame,
            text="📊 订阅信息",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        sub_title.pack(pady=(15, 10))
        
        # 订阅信息内容区域
        self.subscription_content_frame = ctk.CTkFrame(self.subscription_frame)
        self.subscription_content_frame.pack(fill="x", padx=15, pady=(0, 15))
    
    def create_system_detection_section(self):
        """创建系统检测区域"""
        # 系统检测框架
        self.detection_frame = ctk.CTkFrame(self.main_container)
        self.detection_frame.pack(fill="x", padx=10, pady=(0, 15))
        
        # 区域标题
        detection_title = ctk.CTkLabel(
            self.detection_frame,
            text="🔍 系统检测",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        detection_title.pack(pady=(15, 10))
        
        # 系统检测内容区域
        self.detection_content_frame = ctk.CTkFrame(self.detection_frame)
        self.detection_content_frame.pack(fill="x", padx=15, pady=(0, 15))
    
    def create_activity_section(self):
        """创建活动状态区域"""
        # 活动状态框架
        self.activity_frame = ctk.CTkFrame(self.main_container)
        self.activity_frame.pack(fill="x", padx=10, pady=(0, 15))
        
        # 区域标题
        activity_title = ctk.CTkLabel(
            self.activity_frame,
            text="📈 活动状态",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        activity_title.pack(pady=(15, 10))
        
        # 活动状态内容区域
        self.activity_content_frame = ctk.CTkFrame(self.activity_frame)
        self.activity_content_frame.pack(fill="x", padx=15, pady=(0, 15))
    
    def create_action_buttons_section(self):
        """创建操作按钮区域"""
        # 操作按钮框架
        self.actions_frame = ctk.CTkFrame(self.main_container)
        self.actions_frame.pack(fill="x", padx=10, pady=(0, 20))
        
        # 区域标题
        actions_title = ctk.CTkLabel(
            self.actions_frame,
            text="🎮 账号操作",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        actions_title.pack(pady=(15, 10))
        
        # 按钮容器
        buttons_container = ctk.CTkFrame(self.actions_frame)
        buttons_container.pack(fill="x", padx=15, pady=(0, 15))
        
        # 第一行按钮
        button_row1 = ctk.CTkFrame(buttons_container)
        button_row1.pack(fill="x", pady=(10, 5))
        
        self.refresh_button = ctk.CTkButton(
            button_row1,
            text="🔄 刷新状态",
            command=self.refresh_account_status,
            width=120,
            height=35
        )
        self.refresh_button.pack(side="left", padx=(10, 5))
        
        self.login_button = ctk.CTkButton(
            button_row1,
            text="🔑 登录账号",
            command=self.show_login_dialog,
            width=120,
            height=35
        )
        self.login_button.pack(side="left", padx=5)
        
        self.logout_button = ctk.CTkButton(
            button_row1,
            text="🚪 退出登录",
            command=self.logout_account,
            width=120,
            height=35
        )
        self.logout_button.pack(side="left", padx=5)
        
        # 第二行按钮
        button_row2 = ctk.CTkFrame(buttons_container)
        button_row2.pack(fill="x", pady=(5, 10))
        
        self.validate_button = ctk.CTkButton(
            button_row2,
            text="✅ 验证Cookie",
            command=self.validate_current_cookie,
            width=120,
            height=35
        )
        self.validate_button.pack(side="left", padx=(10, 5))
        
        self.subscription_button = ctk.CTkButton(
            button_row2,
            text="📊 刷新订阅",
            command=self.refresh_subscription_info,
            width=120,
            height=35
        )
        self.subscription_button.pack(side="left", padx=5)
    
    def refresh_account_status(self):
        """刷新账号状态"""
        if self.refresh_in_progress:
            return
        
        self.refresh_in_progress = True
        self.refresh_button.configure(text="🔄 刷新中...", state="disabled")
        
        # 在后台线程中执行刷新
        thread = threading.Thread(target=self._perform_refresh)
        thread.daemon = True
        thread.start()
    
    def _perform_refresh(self):
        """执行刷新操作"""
        try:
            self.log_callback("🔄 开始刷新账号状态...", "INFO")
            
            # 刷新各个区域
            self._refresh_login_status()
            self._refresh_subscription_info()
            self._refresh_system_detection()
            self._refresh_activity_status()
            
            self.log_callback("✅ 账号状态刷新完成", "SUCCESS")
            
        except Exception as e:
            self.log_callback(f"❌ 刷新账号状态失败: {str(e)}", "ERROR")
        
        finally:
            # 重置按钮状态
            self.main_container.after(0, lambda: self.refresh_button.configure(
                text="🔄 刷新状态", state="normal"
            ))
            self.refresh_in_progress = False
    
    def _refresh_login_status(self):
        """刷新登录状态"""
        # 清空现有内容
        for widget in self.login_content_frame.winfo_children():
            widget.destroy()
        
        try:
            current_info = get_current_login_info()
            
            if current_info:
                # 已登录状态
                status_label = ctk.CTkLabel(
                    self.login_content_frame,
                    text="✅ 已登录",
                    text_color="green",
                    font=ctk.CTkFont(size=16, weight="bold")
                )
                status_label.pack(pady=(10, 5))
                
                # 账号信息
                info_items = [
                    ("📧 邮箱", current_info['email']),
                    ("👤 用户名", current_info['username']),
                    ("🕒 登录时间", current_info['last_used']),
                    ("🍪 Cookie长度", f"{len(current_info.get('cookie_string', ''))} 字符")
                ]
                
                for label, value in info_items:
                    item_frame = ctk.CTkFrame(self.login_content_frame)
                    item_frame.pack(fill="x", padx=10, pady=2)
                    
                    label_widget = ctk.CTkLabel(
                        item_frame,
                        text=f"{label}:",
                        font=ctk.CTkFont(size=12, weight="bold"),
                        width=100
                    )
                    label_widget.pack(side="left", padx=(10, 5), pady=5)
                    
                    value_widget = ctk.CTkLabel(
                        item_frame,
                        text=str(value),
                        font=ctk.CTkFont(size=12)
                    )
                    value_widget.pack(side="left", padx=5, pady=5)
                
                # 更新按钮状态
                self.main_container.after(0, lambda: self.login_button.configure(text="🔄 更换账号"))
                self.main_container.after(0, lambda: self.logout_button.configure(state="normal"))
                
            else:
                # 未登录状态
                status_label = ctk.CTkLabel(
                    self.login_content_frame,
                    text="❌ 未登录",
                    text_color="red",
                    font=ctk.CTkFont(size=16, weight="bold")
                )
                status_label.pack(pady=(10, 5))
                
                hint_label = ctk.CTkLabel(
                    self.login_content_frame,
                    text="请点击下方按钮登录您的AugmentCode账号",
                    font=ctk.CTkFont(size=12),
                    text_color="gray"
                )
                hint_label.pack(pady=(0, 10))
                
                # 更新按钮状态
                self.main_container.after(0, lambda: self.login_button.configure(text="🔑 登录账号"))
                self.main_container.after(0, lambda: self.logout_button.configure(state="disabled"))
                
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.login_content_frame,
                text=f"❌ 获取登录状态失败: {str(e)}",
                text_color="red",
                font=ctk.CTkFont(size=12)
            )
            error_label.pack(pady=10)
    
    def _refresh_subscription_info(self):
        """刷新订阅信息"""
        # 清空现有内容
        for widget in self.subscription_content_frame.winfo_children():
            widget.destroy()
        
        try:
            current_info = get_current_login_info()
            
            if not current_info:
                no_login_label = ctk.CTkLabel(
                    self.subscription_content_frame,
                    text="⚠️ 需要先登录才能查看订阅信息",
                    text_color="orange",
                    font=ctk.CTkFont(size=12)
                )
                no_login_label.pack(pady=10)
                return
            
            # 获取订阅信息
            success, sub_info = get_account_subscription_info()
            
            if success:
                # 订阅信息获取成功
                status_label = ctk.CTkLabel(
                    self.subscription_content_frame,
                    text="✅ 订阅信息获取成功",
                    text_color="green",
                    font=ctk.CTkFont(size=14, weight="bold")
                )
                status_label.pack(pady=(10, 5))
                
                # 订阅详情
                sub_items = []
                
                if sub_info.get('plan_name'):
                    sub_items.append(("📋 订阅计划", sub_info['plan_name']))
                
                if sub_info.get('usage_limit', 0) > 0:
                    usage_count = sub_info.get('usage_count', 0)
                    usage_limit = sub_info.get('usage_limit', 0)
                    remaining = sub_info.get('remaining_count', 0)
                    usage_percent = (usage_count / usage_limit) * 100 if usage_limit > 0 else 0
                    
                    sub_items.extend([
                        ("🔢 使用情况", f"{usage_count}/{usage_limit} ({usage_percent:.1f}%)"),
                        ("⚡ 剩余次数", f"{remaining} 次"),
                    ])
                    
                    # 剩余次数状态指示
                    if remaining > 50:
                        remaining_status = "🟢 充足"
                        remaining_color = "green"
                    elif remaining > 10:
                        remaining_status = "🟡 注意"
                        remaining_color = "orange"
                    else:
                        remaining_status = "🔴 紧急"
                        remaining_color = "red"
                    
                    sub_items.append(("📊 状态评估", remaining_status))
                else:
                    sub_items.append(("🔢 已使用", f"{sub_info.get('usage_count', 0)} 次"))
                
                if sub_info.get('reset_date'):
                    sub_items.append(("🔄 重置日期", sub_info['reset_date']))
                
                if sub_info.get('subscription_status'):
                    sub_items.append(("✅ 订阅状态", sub_info['subscription_status']))
                
                # 显示订阅项目
                for label, value in sub_items:
                    item_frame = ctk.CTkFrame(self.subscription_content_frame)
                    item_frame.pack(fill="x", padx=10, pady=2)
                    
                    label_widget = ctk.CTkLabel(
                        item_frame,
                        text=f"{label}:",
                        font=ctk.CTkFont(size=12, weight="bold"),
                        width=120
                    )
                    label_widget.pack(side="left", padx=(10, 5), pady=5)
                    
                    # 根据内容设置颜色
                    text_color = "default"
                    if "🟢" in value:
                        text_color = "green"
                    elif "🟡" in value:
                        text_color = "orange"
                    elif "🔴" in value:
                        text_color = "red"
                    
                    value_widget = ctk.CTkLabel(
                        item_frame,
                        text=str(value),
                        font=ctk.CTkFont(size=12),
                        text_color=text_color if text_color != "default" else None
                    )
                    value_widget.pack(side="left", padx=5, pady=5)
                
            else:
                # 订阅信息获取失败
                error_label = ctk.CTkLabel(
                    self.subscription_content_frame,
                    text="❌ 订阅信息获取失败",
                    text_color="red",
                    font=ctk.CTkFont(size=14, weight="bold")
                )
                error_label.pack(pady=(10, 5))
                
                error_detail = ctk.CTkLabel(
                    self.subscription_content_frame,
                    text=f"错误信息: {sub_info.get('error', '未知错误')}",
                    text_color="red",
                    font=ctk.CTkFont(size=11)
                )
                error_detail.pack(pady=(0, 10))
                
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.subscription_content_frame,
                text=f"❌ 订阅信息查询异常: {str(e)}",
                text_color="red",
                font=ctk.CTkFont(size=12)
            )
            error_label.pack(pady=10)

    def _refresh_system_detection(self):
        """刷新系统检测信息"""
        # 导入并应用方法
        from .account_status_methods import add_system_detection_methods
        add_system_detection_methods(self)
        return self._refresh_system_detection()

    def _refresh_activity_status(self):
        """刷新活动状态信息"""
        # 导入并应用方法
        from .account_status_methods import add_system_detection_methods
        add_system_detection_methods(self)
        return self._refresh_activity_status()

    def show_login_dialog(self):
        """显示登录对话框"""
        # 导入并应用方法
        from .account_status_methods import add_action_methods
        add_action_methods(self)
        return self.show_login_dialog()

    def logout_account(self):
        """退出登录"""
        # 导入并应用方法
        from .account_status_methods import add_action_methods
        add_action_methods(self)
        return self.logout_account()

    def validate_current_cookie(self):
        """验证当前Cookie"""
        # 导入并应用方法
        from .account_status_methods import add_action_methods
        add_action_methods(self)
        return self.validate_current_cookie()

    def refresh_subscription_info(self):
        """刷新订阅信息"""
        # 导入并应用方法
        from .account_status_methods import add_action_methods
        add_action_methods(self)
        return self.refresh_subscription_info()
