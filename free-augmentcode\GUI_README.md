# Free AugmentCode GUI 版本

## 🎨 图形用户界面

这是 Free AugmentCode 的图形用户界面版本，提供了更加友好和直观的操作体验。

## 🚀 快速启动

### 方法一：使用批处理文件（推荐）
双击 `start_gui.bat` 文件即可启动GUI界面。

### 方法二：使用Python脚本
```bash
python start_gui.py
```

### 方法三：直接运行GUI
```bash
python gui.py
```

## 📋 功能特性

### 🖥️ 界面布局
- **左侧面板**：
  - 📁 系统路径信息显示
  - 🎮 操作控制面板
  - ⚠️ 使用前警告提示
  - 📊 进度条显示

- **右侧面板**：
  - 📊 操作结果展示
  - 📝 详细操作日志
  - 🧹 日志清理功能

- **底部状态栏**：
  - 实时状态显示
  - 版本信息

### 🔧 核心功能
1. **📝 Telemetry ID 修改**
   - 自动生成新的机器ID和设备ID
   - 自动备份原始数据
   - 实时显示新旧ID对比

2. **🗃️ 数据库清理**
   - 清理SQLite数据库中的AugmentCode记录
   - 自动备份数据库文件
   - 显示删除的记录数量

3. **💾 工作区存储管理**
   - 清理工作区存储文件
   - 创建ZIP格式备份
   - 显示删除的文件数量

### 🛡️ 安全特性
- **自动备份**：所有操作前自动创建备份
- **确认对话框**：执行前需要用户确认
- **详细日志**：记录所有操作步骤和结果
- **错误处理**：友好的错误提示和处理

## 📖 使用说明

### 使用前准备
1. **完全退出 VS Code**
2. **退出 AugmentCode 插件**
3. **确保有管理员权限**（如果需要）

### 操作步骤
1. 启动GUI界面
2. 查看系统路径信息，确认文件存在（绿色✓表示存在）
3. 阅读警告提示，确保已退出VS Code
4. 点击"🚀 开始清理 AugmentCode 数据"按钮
5. 在确认对话框中点击"是"
6. 等待操作完成（可查看进度条和日志）
7. 操作完成后查看结果
8. 重新启动VS Code并使用新邮箱登录

### 界面说明

#### 系统路径信息
- **用户目录**：当前用户的主目录
- **应用数据目录**：VS Code数据存储目录
- **存储文件**：VS Code的storage.json文件
- **数据库文件**：VS Code的state.vscdb文件
- **机器ID文件**：VS Code的machineid文件
- **工作区存储**：VS Code的workspaceStorage目录

#### 状态指示器
- ✅ **绿色勾号**：文件/目录存在
- ❌ **红色叉号**：文件/目录不存在

#### 进度显示
- **进度条**：显示当前操作进度（0-100%）
- **进度文本**：显示当前执行的步骤

#### 操作日志
- **时间戳**：每条日志都有精确的时间记录
- **日志级别**：
  - `INFO`：一般信息
  - `SUCCESS`：成功操作
  - `WARNING`：警告信息
  - `ERROR`：错误信息

## 🔧 技术细节

### 依赖要求
- Python 3.10+
- customtkinter >= 5.2.0
- pillow >= 10.0.0

### 文件结构
```
free-augmentcode/
├── gui.py              # GUI主程序
├── start_gui.py        # GUI启动脚本
├── start_gui.bat       # Windows批处理启动文件
├── requirements.txt    # Python依赖列表
├── index.py           # 命令行版本
├── augutils/          # 核心功能模块
├── utils/             # 工具模块
└── GUI_README.md      # GUI使用说明
```

### 线程安全
- GUI使用多线程设计，避免界面冻结
- 后台线程执行耗时操作
- 主线程负责界面更新

## ⚠️ 注意事项

1. **备份重要性**：虽然程序会自动备份，但建议手动备份重要数据
2. **权限问题**：某些系统可能需要管理员权限
3. **防病毒软件**：某些防病毒软件可能误报，请添加信任
4. **网络连接**：首次运行可能需要下载依赖包

## 🐛 故障排除

### 常见问题

**Q: GUI无法启动**
A: 检查Python版本是否为3.10+，确保已安装所需依赖

**Q: 提示文件不存在**
A: 确保VS Code已安装并至少运行过一次

**Q: 操作失败**
A: 检查是否完全退出了VS Code和AugmentCode插件

**Q: 权限被拒绝**
A: 尝试以管理员身份运行程序

### 日志分析
- 查看操作日志中的ERROR级别消息
- 检查文件路径是否正确
- 确认备份文件是否创建成功

## 📞 支持

如果遇到问题，请：
1. 查看操作日志中的错误信息
2. 检查系统路径信息是否正确
3. 确认所有使用前准备步骤已完成
4. 提供详细的错误日志信息

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。
