#!/usr/bin/env python3
"""
测试AugmentCode检测功能
验证扩展安装、登录状态和订阅信息的显示
"""

from utils.account_detector import get_all_account_info, _detect_augment_extension, _get_extension_user_info

def test_detection():
    """测试检测功能"""
    print("🔍 测试AugmentCode检测功能")
    print("=" * 50)
    
    # 1. 测试扩展检测
    print("\n1️⃣ 扩展安装检测:")
    extension_detected = _detect_augment_extension()
    print(f"   扩展状态: {'✅ 已安装' if extension_detected else '❌ 未安装'}")
    
    # 2. 测试用户信息获取
    print("\n2️⃣ 用户信息检测:")
    user_info = _get_extension_user_info()
    if user_info:
        print(f"   邮箱: {user_info.get('email', '未检测到')}")
        print(f"   用户名: {user_info.get('username', '未检测到')}")
        print(f"   用户ID: {user_info.get('user_id', '未检测到')}")
        if 'detected_method' in user_info:
            print(f"   检测方式: {user_info.get('detected_method')}")
    else:
        print("   ❌ 未检测到用户信息")
    
    # 3. 测试完整账号信息
    print("\n3️⃣ 完整账号信息:")
    all_info = get_all_account_info()
    
    augment_account = all_info.get('augment_account', {})
    print(f"   登录状态: {'✅ 已登录' if augment_account.get('logged_in') else '❌ 未登录'}")
    
    if augment_account.get('logged_in'):
        print(f"   登录方式: {augment_account.get('login_method', '未知')}")
        
        # 订阅信息
        subscription_info = augment_account.get('subscription_info', {})
        if subscription_info:
            print(f"   订阅计划: {subscription_info.get('plan', '未知')}")
            print(f"   订阅状态: {subscription_info.get('status', '未知')}")
        
        # Cookie信息
        cookie_info = augment_account.get('cookie_info', {})
        if cookie_info:
            print(f"   Cookie状态: {'已检测到' if cookie_info.get('has_cookie') else '未检测到'}")
    
    # 4. 测试活动检测
    print("\n4️⃣ 活动状态检测:")
    activity_info = all_info.get('augment_activity', {})
    print(f"   活动状态: {'✅ 检测到活动' if activity_info.get('is_active') else '❌ 未检测到活动'}")
    
    if activity_info.get('is_active'):
        if activity_info.get('extension_confirmed'):
            print("   ✅ 扩展已确认")
        if activity_info.get('publisher_trusted'):
            print("   ✅ 发布者已信任")
        if activity_info.get('chat_history'):
            print("   ✅ 有聊天历史")
        
        webview_sessions = activity_info.get('webview_sessions', [])
        if webview_sessions:
            print(f"   ✅ 检测到 {len(webview_sessions)} 个会话")
    
    # 5. 汇总信息
    print("\n5️⃣ 检测汇总:")
    summary = all_info.get('summary', {})
    print(f"   AugmentCode登录: {'是' if summary.get('has_augment_login') else '否'}")
    print(f"   AugmentCode活跃: {'是' if summary.get('is_augment_active') else '否'}")
    print(f"   总账号数: {summary.get('total_accounts', 0)}")
    
    print("\n" + "=" * 50)
    print("🎉 检测测试完成！")

if __name__ == "__main__":
    test_detection()
