#!/usr/bin/env python3
"""
账号检测工具
检测系统中的AugmentCode账号信息
"""

import os
import json
import sqlite3
import re
from typing import Dict, List, Optional
from datetime import datetime
from .paths import get_storage_path, get_db_path, get_workspace_storage_path
import platform


def get_vscode_path():
    """获取VS Code配置路径

    Returns:
        str: VS Code配置路径
    """
    if platform.system() == "Windows":
        return os.path.expandvars("%APPDATA%/Code")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Library/Application Support/Code")
    else:  # Linux
        return os.path.expanduser("~/.config/Code")


def _detect_augment_extension() -> bool:
    """
    检测AugmentCode扩展是否已安装

    Returns:
        bool: 如果检测到AugmentCode扩展则返回True
    """
    try:
        vscode_path = get_vscode_path()

        # 1. 检查扩展目录中是否有augment.vscode-augment
        extensions_path = os.path.join(vscode_path, "extensions")
        if os.path.exists(extensions_path):
            for item in os.listdir(extensions_path):
                # 查找augment.vscode-augment开头的目录
                if item.startswith('augment.vscode-augment'):
                    extension_path = os.path.join(extensions_path, item)
                    if os.path.isdir(extension_path):
                        package_json = os.path.join(extension_path, "package.json")
                        if os.path.exists(package_json):
                            try:
                                with open(package_json, 'r', encoding='utf-8') as f:
                                    package_data = json.load(f)
                                    # 检查是否是正确的AugmentCode扩展
                                    if (package_data.get('name') == 'vscode-augment' and
                                        package_data.get('publisher') == 'augment'):
                                        return True
                            except:
                                continue

        # 2. 检查globalStorage中是否有augment.vscode-augment目录
        global_storage_path = os.path.join(vscode_path, "User", "globalStorage")
        if os.path.exists(global_storage_path):
            augment_storage = os.path.join(global_storage_path, "augment.vscode-augment")
            if os.path.exists(augment_storage):
                return True

        # 3. 检查state.vscdb中是否有augment相关记录
        state_db_path = os.path.join(vscode_path, "User", "globalStorage", "state.vscdb")
        if os.path.exists(state_db_path):
            try:
                import sqlite3
                conn = sqlite3.connect(state_db_path)
                cursor = conn.cursor()

                # 查找augment.vscode-augment相关的记录
                cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment.vscode-augment%'")
                rows = cursor.fetchall()
                conn.close()

                if rows:
                    return True
            except:
                pass

        # 4. 检查用户设置中的扩展配置
        user_settings_path = os.path.join(vscode_path, "User", "settings.json")
        if os.path.exists(user_settings_path):
            try:
                with open(user_settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    # 检查扩展相关设置
                    for key, value in settings.items():
                        if 'augment' in key.lower():
                            return True
                        if isinstance(value, str) and 'augment.vscode-augment' in value:
                            return True
                        if isinstance(value, list):
                            for item in value:
                                if isinstance(item, str) and 'augment.vscode-augment' in item:
                                    return True
            except:
                pass

        return False

    except Exception:
        return False


def _get_extension_user_info() -> Dict:
    """
    从扩展数据中获取用户信息

    Returns:
        dict: 用户信息字典
    """
    user_info = {}

    try:
        vscode_path = get_vscode_path()

        # 1. 检查工作区存储中的聊天历史（可能包含用户活动信息）
        workspace_storage = os.path.join(vscode_path, "User", "workspaceStorage")
        if os.path.exists(workspace_storage):
            for workspace_dir in os.listdir(workspace_storage):
                workspace_path = os.path.join(workspace_storage, workspace_dir)
                if os.path.isdir(workspace_path):
                    state_file = os.path.join(workspace_path, "state.vscdb")
                    if os.path.exists(state_file):
                        try:
                            import sqlite3
                            conn = sqlite3.connect(state_file)
                            cursor = conn.cursor()

                            # 查找augment聊天数据
                            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment-chat%'")
                            rows = cursor.fetchall()

                            for key, value in rows:
                                if 'webviewState' in value:
                                    try:
                                        # 解析webview状态
                                        data = json.loads(value)
                                        webview_state = data.get('webviewState', '')
                                        if webview_state:
                                            chat_data = json.loads(webview_state)
                                            # 如果有对话历史，说明用户在使用
                                            conversations = chat_data.get('conversations', {})
                                            if conversations:
                                                user_info = {
                                                    'email': '已登录用户@augmentcode.com',
                                                    'username': 'AugmentCode活跃用户',
                                                    'user_id': 'active_chat_user',
                                                    'detected_method': 'chat_activity',
                                                    'chat_conversations': len(conversations),
                                                    'current_conversation': chat_data.get('currentConversationId', '')
                                                }
                                                conn.close()
                                                return user_info
                                    except:
                                        continue

                            conn.close()
                        except:
                            continue

        # 2. 检查全局状态中的webview信息
        state_db_path = os.path.join(vscode_path, "User", "globalStorage", "state.vscdb")
        if os.path.exists(state_db_path) and not user_info:
            try:
                import sqlite3
                conn = sqlite3.connect(state_db_path)
                cursor = conn.cursor()

                # 查找webview相关的augment数据
                cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%webview%' AND value LIKE '%augment%'")
                rows = cursor.fetchall()

                webview_sessions = 0
                for key, value in rows:
                    if 'augment-chat' in value or 'augment-next-edit' in value or 'augmentSettingsPanel' in value:
                        webview_sessions += 1

                if webview_sessions > 0:
                    user_info = {
                        'email': '已登录用户@augmentcode.com',
                        'username': 'AugmentCode用户',
                        'user_id': 'webview_user',
                        'detected_method': 'webview_sessions',
                        'webview_sessions': webview_sessions
                    }

                conn.close()
            except:
                pass

        # 3. 检查扩展确认状态
        if not user_info and _detect_augment_extension():
            try:
                import sqlite3
                conn = sqlite3.connect(state_db_path)
                cursor = conn.cursor()

                # 检查扩展是否被确认
                cursor.execute("SELECT value FROM ItemTable WHERE key = 'extensionUrlHandler.confirmedExtensions'")
                result = cursor.fetchone()

                if result and 'augment.vscode-augment' in result[0]:
                    user_info = {
                        'email': '已确认用户@augmentcode.com',
                        'username': 'AugmentCode确认用户',
                        'user_id': 'confirmed_user',
                        'detected_method': 'extension_confirmed'
                    }

                conn.close()
            except:
                pass

        # 4. 如果检测到扩展但没有其他信息，设置基本信息
        if not user_info and _detect_augment_extension():
            user_info = {
                'email': '扩展用户@augmentcode.com',
                'username': 'AugmentCode扩展用户',
                'user_id': 'extension_user',
                'detected_method': 'extension_installed'
            }

    except Exception:
        pass

    return user_info


def get_vscode_accounts() -> Dict:
    """
    获取VS Code中的账号信息
    
    Returns:
        dict: 包含账号信息的字典
        {
            'github_accounts': list,
            'microsoft_accounts': list,
            'current_user': str,
            'last_login': str
        }
    """
    result = {
        'github_accounts': [],
        'microsoft_accounts': [],
        'current_user': '',
        'last_login': '',
        'session_info': {}
    }
    
    try:
        storage_path = get_storage_path()
        if not os.path.exists(storage_path):
            return result
        
        with open(storage_path, 'r', encoding='utf-8') as f:
            storage_data = json.load(f)
        
        # 查找GitHub账号信息
        github_keys = [key for key in storage_data.keys() if 'github' in key.lower()]
        for key in github_keys:
            if 'user' in key.lower() or 'account' in key.lower():
                value = storage_data[key]
                if isinstance(value, str) and '@' in value:
                    result['github_accounts'].append({
                        'email': value,
                        'source': key,
                        'type': 'GitHub'
                    })
                elif isinstance(value, dict):
                    if 'login' in value:
                        result['github_accounts'].append({
                            'username': value.get('login', ''),
                            'email': value.get('email', ''),
                            'name': value.get('name', ''),
                            'source': key,
                            'type': 'GitHub'
                        })
        
        # 查找Microsoft账号信息
        ms_keys = [key for key in storage_data.keys() if any(term in key.lower() for term in ['microsoft', 'azure', 'outlook', 'live'])]
        for key in ms_keys:
            value = storage_data[key]
            if isinstance(value, str) and '@' in value:
                result['microsoft_accounts'].append({
                    'email': value,
                    'source': key,
                    'type': 'Microsoft'
                })
            elif isinstance(value, dict):
                if 'username' in value or 'email' in value:
                    result['microsoft_accounts'].append({
                        'username': value.get('username', ''),
                        'email': value.get('email', ''),
                        'name': value.get('displayName', ''),
                        'source': key,
                        'type': 'Microsoft'
                    })
        
        # 查找当前用户信息
        user_keys = [key for key in storage_data.keys() if 'currentuser' in key.lower() or 'activeuser' in key.lower()]
        for key in user_keys:
            value = storage_data[key]
            if isinstance(value, str):
                result['current_user'] = value
                break
        
        # 查找会话信息
        session_keys = [key for key in storage_data.keys() if 'session' in key.lower() or 'token' in key.lower()]
        for key in session_keys:
            if len(key) < 100:  # 避免处理过长的key
                result['session_info'][key] = str(storage_data[key])[:100] + '...' if len(str(storage_data[key])) > 100 else storage_data[key]
        
    except (json.JSONDecodeError, OSError, PermissionError) as e:
        result['error'] = str(e)
    
    return result


def get_augment_account_info() -> Dict:
    """
    获取AugmentCode扩展的账号信息
    现在从用户配置的账号信息中读取，而不是自动检测

    Returns:
        dict: 包含AugmentCode账号信息的字典
    """
    result = {
        'logged_in': False,
        'email': '',
        'username': '',
        'user_id': '',
        'subscription_info': {},
        'login_time': '',
        'token_info': {},
        'usage_stats': {},
        'login_method': 'cookie',
        'cookie_info': {}
    }

    try:
        # 从账号管理器获取当前登录信息
        from .account_manager import get_current_login_info

        login_info = get_current_login_info()
        if login_info:
            result['logged_in'] = True
            result['email'] = login_info.get('email', '')
            result['username'] = login_info.get('username', '')
            result['login_time'] = login_info.get('last_used', '')
            result['user_id'] = login_info.get('email', '').replace('@', '_').replace('.', '_')

            # Cookie信息（隐藏敏感部分）
            cookie_string = login_info.get('cookie_string', '')
            if cookie_string:
                result['cookie_info'] = {
                    'has_cookie': True,
                    'cookie_length': len(cookie_string),
                    'cookie_preview': cookie_string[:20] + '...' if len(cookie_string) > 20 else cookie_string
                }

            # 模拟订阅信息
            result['subscription_info'] = {
                'plan': 'Cookie登录',
                'status': 'active',
                'login_method': 'cookie'
            }

            # 模拟使用统计
            result['usage_stats'] = {
                'login_method': 'cookie',
                'account_source': 'user_input'
            }

        # 如果没有配置的登录信息，尝试从旧的检测方式获取（兼容性）
        if not result['logged_in']:
            result = _legacy_detect_account_info(result)

    except Exception as e:
        result['error'] = str(e)

    return result


def _legacy_detect_account_info(result: Dict) -> Dict:
    """
    旧版账号检测逻辑（用于兼容性）

    Args:
        result: 现有的结果字典

    Returns:
        dict: 更新后的结果字典
    """
    try:
        # 首先检测AugmentCode扩展是否已安装
        extension_detected = _detect_augment_extension()
        if extension_detected:
            result['logged_in'] = True
            result['login_method'] = 'extension'
            result['subscription_info'] = {
                'plan': 'AugmentCode扩展',
                'status': 'installed',
                'login_method': 'extension'
            }

            # 尝试从扩展数据中获取用户信息
            extension_user_info = _get_extension_user_info()
            if extension_user_info:
                result.update(extension_user_info)

        # 从storage.json中查找AugmentCode相关信息
        storage_path = get_storage_path()
        if os.path.exists(storage_path):
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)

            # 查找Augment相关的键
            augment_keys = [key for key in storage_data.keys() if 'augment' in key.lower()]

            for key in augment_keys:
                value = storage_data[key]

                # 查找用户信息
                if 'user' in key.lower():
                    if isinstance(value, dict):
                        result['email'] = value.get('email', '')
                        result['username'] = value.get('username', value.get('name', ''))
                        result['user_id'] = value.get('id', value.get('userId', ''))
                        if result['email'] or result['username']:
                            result['logged_in'] = True
                    elif isinstance(value, str) and '@' in value:
                        result['email'] = value
                        result['logged_in'] = True

                # 查找认证信息
                elif 'auth' in key.lower() or 'token' in key.lower():
                    if isinstance(value, dict):
                        result['token_info'][key] = {k: str(v)[:50] + '...' if len(str(v)) > 50 else v for k, v in value.items()}
                    elif isinstance(value, str) and len(value) > 10:
                        result['token_info'][key] = value[:50] + '...'
                        result['logged_in'] = True

                # 查找订阅信息
                elif 'subscription' in key.lower() or 'plan' in key.lower():
                    result['subscription_info'][key] = value

                # 查找使用统计
                elif 'usage' in key.lower() or 'stats' in key.lower():
                    result['usage_stats'][key] = value
        
        # 从数据库中查找更多信息
        db_path = get_db_path()
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 查询包含augment的记录
                cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%' OR value LIKE '%augment%'")
                rows = cursor.fetchall()

                for key, value in rows:
                    try:
                        # 检查扩展确认信息
                        if 'extensionUrlHandler.confirmedExtensions' in key:
                            if 'augment.vscode-augment' in value:
                                result['logged_in'] = True
                                result['session_info']['extension_confirmed'] = True

                        # 检查信任的发布者
                        elif 'extensions.trustedPublishers' in key:
                            if 'augment' in value.lower():
                                result['logged_in'] = True
                                result['session_info']['publisher_trusted'] = True
                                # 尝试解析JSON获取更多信息
                                try:
                                    parsed_value = json.loads(value)
                                    if 'augment' in parsed_value:
                                        augment_info = parsed_value['augment']
                                        if isinstance(augment_info, dict):
                                            result['session_info']['publisher_info'] = augment_info
                                except:
                                    pass

                        # 检查webview相关信息
                        elif 'webview' in key.lower() and 'augment' in value.lower():
                            result['logged_in'] = True
                            result['session_info']['webview_active'] = True

                        # 尝试解析JSON值
                        if value and value.startswith('{'):
                            try:
                                parsed_value = json.loads(value)

                                # 查找用户相关信息
                                if 'user' in key.lower():
                                    if isinstance(parsed_value, dict):
                                        if 'email' in parsed_value:
                                            result['email'] = parsed_value['email']
                                            result['logged_in'] = True
                                        if 'username' in parsed_value or 'name' in parsed_value:
                                            result['username'] = parsed_value.get('username', parsed_value.get('name', ''))

                                # 查找登录时间
                                elif 'login' in key.lower() or 'session' in key.lower():
                                    if 'timestamp' in parsed_value:
                                        try:
                                            timestamp = parsed_value['timestamp']
                                            if isinstance(timestamp, (int, float)):
                                                result['login_time'] = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                                        except:
                                            pass

                                # 递归搜索嵌套对象中的用户信息
                                def search_nested_user_info(obj, path=""):
                                    if isinstance(obj, dict):
                                        for k, v in obj.items():
                                            if k.lower() in ['email', 'user', 'username', 'account']:
                                                if isinstance(v, str) and '@' in v:
                                                    result['email'] = v
                                                    result['logged_in'] = True
                                                elif isinstance(v, str) and v:
                                                    result['username'] = v
                                                    result['logged_in'] = True
                                            elif isinstance(v, (dict, list)):
                                                search_nested_user_info(v, f"{path}.{k}")
                                    elif isinstance(obj, list):
                                        for i, item in enumerate(obj):
                                            if isinstance(item, (dict, list)):
                                                search_nested_user_info(item, f"{path}[{i}]")

                                search_nested_user_info(parsed_value)

                            except json.JSONDecodeError:
                                pass

                        # 如果不是JSON，检查是否是邮箱
                        elif value and '@' in value and '.' in value and len(value) < 100:
                            # 简单的邮箱格式检查
                            import re
                            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                            emails = re.findall(email_pattern, value)
                            if emails:
                                result['email'] = emails[0]
                                result['logged_in'] = True

                    except Exception:
                        continue
                
                conn.close()
                
            except sqlite3.Error:
                pass
    
    except Exception as e:
        result['error'] = str(e)
    
    return result


def detect_augment_activity() -> Dict:
    """
    检测AugmentCode的活跃状态和使用情况

    Returns:
        dict: AugmentCode活跃状态信息
    """
    result = {
        'is_active': False,
        'extension_confirmed': False,
        'publisher_trusted': False,
        'webview_sessions': [],
        'recent_activity': [],
        'settings_accessed': False,
        'chat_history': False
    }

    try:
        db_path = get_db_path()
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查找所有augment相关记录
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%' OR value LIKE '%augment%'")
            rows = cursor.fetchall()

            for key, value in rows:
                # 扩展确认状态
                if 'extensionUrlHandler.confirmedExtensions' in key:
                    if 'augment.vscode-augment' in value:
                        result['extension_confirmed'] = True
                        result['is_active'] = True

                # 信任的发布者
                elif 'extensions.trustedPublishers' in key:
                    if 'augment' in value.lower():
                        result['publisher_trusted'] = True
                        result['is_active'] = True

                # Webview会话
                elif 'webview' in key.lower() and 'augment' in value.lower():
                    result['webview_sessions'].append({
                        'key': key,
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    result['is_active'] = True

                # 设置面板访问
                elif 'augmentSettingsPanel' in value:
                    result['settings_accessed'] = True
                    result['is_active'] = True

                # 聊天相关
                elif 'augment-chat' in value.lower():
                    result['chat_history'] = True
                    result['is_active'] = True

                # 记录最近活动
                if 'augment' in key.lower() or 'augment' in value.lower():
                    result['recent_activity'].append({
                        'key': key,
                        'value_preview': str(value)[:100] + '...' if len(str(value)) > 100 else str(value),
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

            conn.close()

    except Exception as e:
        result['error'] = str(e)

    return result


def get_workspace_account_info() -> Dict:
    """
    从工作区存储中获取账号信息
    
    Returns:
        dict: 工作区中的账号信息
    """
    result = {
        'workspace_accounts': [],
        'recent_projects': [],
        'augment_workspaces': []
    }
    
    try:
        workspace_path = get_workspace_storage_path()
        if not os.path.exists(workspace_path):
            return result
        
        # 遍历工作区存储目录
        for item in os.listdir(workspace_path):
            item_path = os.path.join(workspace_path, item)
            if os.path.isdir(item_path):
                # 查找状态文件
                state_file = os.path.join(item_path, 'state.vscdb')
                if os.path.exists(state_file):
                    try:
                        conn = sqlite3.connect(state_file)
                        cursor = conn.cursor()
                        
                        # 查找用户相关信息
                        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%user%' OR key LIKE '%account%' OR key LIKE '%augment%'")
                        rows = cursor.fetchall()
                        
                        workspace_info = {
                            'workspace_id': item,
                            'accounts': [],
                            'augment_data': []
                        }
                        
                        for key, value in rows:
                            if 'augment' in key.lower():
                                workspace_info['augment_data'].append({
                                    'key': key,
                                    'value': value[:100] + '...' if len(str(value)) > 100 else value
                                })
                            elif '@' in str(value):
                                workspace_info['accounts'].append({
                                    'key': key,
                                    'email': value
                                })
                        
                        if workspace_info['accounts'] or workspace_info['augment_data']:
                            result['workspace_accounts'].append(workspace_info)
                        
                        conn.close()
                        
                    except sqlite3.Error:
                        continue
    
    except (OSError, PermissionError):
        pass
    
    return result


def get_all_account_info() -> Dict:
    """
    获取所有账号信息的汇总

    Returns:
        dict: 完整的账号信息汇总
    """
    result = {
        'vscode_accounts': get_vscode_accounts(),
        'augment_account': get_augment_account_info(),
        'augment_activity': detect_augment_activity(),
        'workspace_info': get_workspace_account_info(),
        'summary': {
            'has_augment_login': False,
            'is_augment_active': False,
            'primary_email': '',
            'account_count': 0,
            'last_activity': '',
            'activity_indicators': []
        }
    }
    
    # 生成汇总信息
    augment_info = result['augment_account']
    augment_activity = result['augment_activity']

    # 检查AugmentCode登录状态
    if augment_info['logged_in'] and augment_info['email']:
        result['summary']['has_augment_login'] = True
        result['summary']['primary_email'] = augment_info['email']

    # 检查AugmentCode活跃状态
    if augment_activity['is_active']:
        result['summary']['is_augment_active'] = True

        # 收集活动指标
        if augment_activity['extension_confirmed']:
            result['summary']['activity_indicators'].append('扩展已确认')
        if augment_activity['publisher_trusted']:
            result['summary']['activity_indicators'].append('发布者已信任')
        if augment_activity['settings_accessed']:
            result['summary']['activity_indicators'].append('设置已访问')
        if augment_activity['chat_history']:
            result['summary']['activity_indicators'].append('聊天功能已使用')
        if augment_activity['webview_sessions']:
            result['summary']['activity_indicators'].append(f'Webview会话: {len(augment_activity["webview_sessions"])}个')

    # 统计账号数量
    account_count = 0
    account_count += len(result['vscode_accounts']['github_accounts'])
    account_count += len(result['vscode_accounts']['microsoft_accounts'])
    if augment_info['logged_in'] or augment_activity['is_active']:
        account_count += 1

    result['summary']['account_count'] = account_count

    # 获取最后活动时间
    if augment_info['login_time']:
        result['summary']['last_activity'] = augment_info['login_time']
    
    return result


def format_account_summary(account_info: Dict) -> str:
    """
    格式化账号信息为可读字符串
    
    Args:
        account_info: 账号信息字典
        
    Returns:
        str: 格式化的账号信息
    """
    lines = []
    
    # AugmentCode账号信息
    augment = account_info['augment_account']
    activity = account_info.get('augment_activity', {})

    # 登录状态
    if augment['logged_in']:
        login_method = augment.get('login_method', 'unknown')
        if login_method == 'cookie':
            lines.append("🔐 AugmentCode 登录状态: ✅ 已登录 (Cookie)")
        else:
            lines.append("🔐 AugmentCode 登录状态: ✅ 已登录")

        if augment['email']:
            lines.append(f"📧 登录邮箱: {augment['email']}")
        if augment['username']:
            lines.append(f"👤 用户名: {augment['username']}")
        if augment['login_time']:
            lines.append(f"🕒 登录时间: {augment['login_time']}")

        # 显示Cookie信息
        if augment.get('cookie_info', {}).get('has_cookie'):
            cookie_info = augment['cookie_info']
            lines.append(f"🍪 Cookie信息: {cookie_info['cookie_length']} 字符")

        if augment['subscription_info']:
            lines.append(f"💳 订阅信息: {len(augment['subscription_info'])} 项")
    else:
        lines.append("🔐 AugmentCode 登录状态: ❌ 未配置登录信息")
        lines.append("💡 请在GUI中输入账号和Cookie信息进行登录")

    # 活跃状态
    if activity.get('is_active'):
        lines.append("🚀 AugmentCode 活跃状态: ✅ 检测到活动")

        activity_details = []
        if activity.get('extension_confirmed'):
            activity_details.append("扩展已确认")
        if activity.get('publisher_trusted'):
            activity_details.append("发布者已信任")
        if activity.get('settings_accessed'):
            activity_details.append("设置已访问")
        if activity.get('chat_history'):
            activity_details.append("聊天功能已使用")
        if activity.get('webview_sessions'):
            activity_details.append(f"Webview会话({len(activity['webview_sessions'])}个)")

        if activity_details:
            lines.append(f"📊 活动指标: {', '.join(activity_details)}")

        if activity.get('recent_activity'):
            lines.append(f"📈 最近活动: {len(activity['recent_activity'])} 项记录")
    else:
        lines.append("🚀 AugmentCode 活跃状态: ❌ 未检测到活动")
    
    # VS Code账号信息
    vscode = account_info['vscode_accounts']
    if vscode['github_accounts']:
        lines.append(f"\n🐙 GitHub 账号: {len(vscode['github_accounts'])} 个")
        for acc in vscode['github_accounts'][:3]:  # 只显示前3个
            if acc.get('email'):
                lines.append(f"  - {acc['email']}")
    
    if vscode['microsoft_accounts']:
        lines.append(f"\n🏢 Microsoft 账号: {len(vscode['microsoft_accounts'])} 个")
        for acc in vscode['microsoft_accounts'][:3]:  # 只显示前3个
            if acc.get('email'):
                lines.append(f"  - {acc['email']}")
    
    # 汇总信息
    summary = account_info['summary']
    lines.append(f"\n📊 账号汇总:")
    lines.append(f"  - 总账号数: {summary['account_count']}")
    lines.append(f"  - AugmentCode 登录: {'是' if summary['has_augment_login'] else '否'}")
    lines.append(f"  - AugmentCode 活跃: {'是' if summary['is_augment_active'] else '否'}")
    if summary['primary_email']:
        lines.append(f"  - 主要邮箱: {summary['primary_email']}")
    if summary['activity_indicators']:
        lines.append(f"  - 活动指标: {', '.join(summary['activity_indicators'])}")
    if summary['last_activity']:
        lines.append(f"  - 最后活动: {summary['last_activity']}")
    
    return "\n".join(lines)


if __name__ == "__main__":
    # 测试功能
    print("🔍 检查账号信息...")
    account_info = get_all_account_info()
    print(format_account_summary(account_info))
