# AugmentCode 账号管理功能说明

## 🎯 功能概述

本工具现在支持通过手动输入账号和Cookie信息来管理AugmentCode登录状态，解决了官方文档中基于Cookie认证的需求。

## ✨ 主要功能

### 1. 账号信息检测
- **自动检测**：扫描VS Code配置文件，检测已安装的AugmentCode扩展
- **活动状态**：检测扩展的使用情况和聊天历史
- **手动输入**：支持用户手动输入账号和Cookie信息

### 2. 手动登录管理
- **邮箱输入**：输入您的AugmentCode账号邮箱
- **用户名设置**：可选的用户名设置
- **Cookie管理**：安全存储和管理Cookie信息
- **状态验证**：自动验证登录状态的有效性

### 3. 用户界面功能
- **状态显示**：清晰显示当前登录状态
- **编辑功能**：已登录时可编辑更新登录信息
- **帮助指南**：内置Cookie获取帮助指南
- **一键清除**：快速清除登录信息

## 🚀 使用方法

### 首次设置

1. **启动工具**
   ```bash
   python main.py
   ```

2. **进入账号管理**
   - 点击左侧菜单的"账号修改"
   - 如果未登录，会显示手动输入界面

3. **获取Cookie**
   - 点击"❓ 获取帮助"查看详细指南
   - 登录 https://augmentcode.com
   - 按F12打开开发者工具获取Cookie

4. **输入登录信息**
   - 邮箱地址：您的AugmentCode账号
   - 用户名：可选，用于显示
   - Cookie字符串：从浏览器复制的完整Cookie

5. **保存并验证**
   - 点击"💾 保存登录信息"
   - 系统自动验证并显示登录状态

### 日常使用

#### 查看登录状态
- 进入"账号管理"页面
- 查看当前登录信息和状态

#### 更新登录信息
- 在已登录状态下点击"✏️ 编辑登录信息"
- 在弹出的对话框中更新信息
- 保存后自动刷新状态

#### 清除登录信息
- 点击"🗑️ 清除登录信息"
- 确认后清除所有保存的登录数据

## 🔧 技术实现

### 数据存储
- **本地存储**：登录信息加密存储在本地配置文件
- **安全性**：Cookie等敏感信息经过处理后存储
- **兼容性**：支持Windows、macOS、Linux系统

### 检测机制
- **扩展检测**：扫描VS Code扩展目录
- **活动检测**：分析globalStorage和workspaceStorage
- **状态同步**：实时更新登录状态显示

### 用户界面
- **响应式设计**：适配不同屏幕尺寸
- **直观操作**：清晰的按钮和状态指示
- **帮助系统**：内置详细的使用指南

## 📋 功能特性

### ✅ 已实现功能
- [x] 手动输入账号和Cookie
- [x] 登录状态检测和显示
- [x] Cookie获取帮助指南
- [x] 登录信息编辑和更新
- [x] 一键清除登录数据
- [x] 自动状态验证
- [x] 安全的本地存储
- [x] 跨平台兼容性

### 🔄 状态管理
- **未登录状态**：显示手动输入界面
- **已登录状态**：显示账号信息和管理选项
- **错误状态**：显示错误信息和重试选项

## 🛡️ 安全说明

### Cookie安全
- **本地存储**：Cookie仅存储在本地，不会上传到任何服务器
- **加密保护**：敏感信息经过适当的处理
- **访问控制**：只有本工具可以访问存储的信息

### 使用建议
- **定期更新**：Cookie可能过期，建议定期更新
- **安全环境**：在安全的环境中使用本工具
- **备份重要**：重要数据请做好备份

## 🔍 故障排除

### 常见问题

**Q: 保存的Cookie无效怎么办？**
A: 
1. 确保已成功登录AugmentCode网站
2. 重新获取最新的Cookie
3. 检查Cookie格式是否完整

**Q: 登录状态显示错误？**
A:
1. 点击"🔄 刷新状态"重新检测
2. 检查网络连接是否正常
3. 重新输入登录信息

**Q: 无法打开编辑对话框？**
A:
1. 确保GUI界面正常运行
2. 重启应用程序
3. 检查系统权限设置

### 技术支持
如果遇到其他问题：
1. 查看控制台错误信息
2. 检查日志文件
3. 重新安装依赖包

## 📝 更新日志

### v1.0.0 (当前版本)
- ✨ 新增手动账号输入功能
- ✨ 新增Cookie管理系统
- ✨ 新增登录状态检测
- ✨ 新增帮助指南系统
- 🔧 优化用户界面体验
- 🛡️ 增强数据安全性

---

**注意**：本工具仅用于管理您自己的AugmentCode账号，请遵守相关服务条款。
