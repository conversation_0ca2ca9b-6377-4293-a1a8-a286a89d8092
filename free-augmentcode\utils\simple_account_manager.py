#!/usr/bin/env python3
"""
简化的账号管理模块
避免循环导入问题
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Optional

def get_app_data_dir():
    """获取应用数据目录"""
    import platform
    if platform.system() == "Windows":
        return os.path.expandvars("%APPDATA%")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Library/Application Support")
    else:  # Linux
        return os.path.expanduser("~/.config")

def get_current_login_info() -> Optional[Dict]:
    """
    获取当前登录信息
    
    Returns:
        Dict or None: 登录信息，如果未登录则返回None
    """
    login_file = os.path.join(get_app_data_dir(), "login_info.json")
    
    if not os.path.exists(login_file):
        return None
    
    try:
        with open(login_file, 'r', encoding='utf-8') as f:
            login_info = json.load(f)
        
        # 检查必要的字段
        if 'email' not in login_info or 'cookie_string' not in login_info:
            return None
            
        return login_info
    except (json.JSONDecodeError, OSError):
        return None

def save_login_info(email: str, cookie_string: str, username: str = "") -> bool:
    """
    保存登录信息
    
    Args:
        email: 登录邮箱
        cookie_string: Cookie字符串
        username: 用户名，可选
        
    Returns:
        bool: 保存成功返回True，否则返回False
    """
    # 确保配置目录存在
    app_data_dir = get_app_data_dir()
    if not os.path.exists(app_data_dir):
        try:
            os.makedirs(app_data_dir, exist_ok=True)
        except OSError:
            return False
    
    # 检查参数有效性
    if not email or not cookie_string:
        return False
    
    # 准备登录信息
    login_info = {
        'email': email,
        'username': username or email.split('@')[0],
        'cookie_string': cookie_string,
        'last_used': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'timestamp': int(time.time())
    }
    
    # 保存登录信息
    try:
        login_file = os.path.join(app_data_dir, "login_info.json")
        with open(login_file, 'w', encoding='utf-8') as f:
            json.dump(login_info, f, indent=4)
        return True
    except OSError:
        return False

def clear_login_info() -> bool:
    """
    清除登录信息
    
    Returns:
        bool: 清除成功返回True，否则返回False
    """
    login_file = os.path.join(get_app_data_dir(), "login_info.json")
    
    if os.path.exists(login_file):
        try:
            os.remove(login_file)
            return True
        except OSError:
            return False
    return True

def format_account_status() -> str:
    """
    格式化账号状态信息
    
    Returns:
        str: 格式化的状态信息
    """
    # 获取当前登录信息
    login_info = get_current_login_info()
    if not login_info:
        return "❌ 未登录"

    lines = []
    lines.append(f"✅ 已登录: {login_info['email']}")
    lines.append(f"👤 用户名: {login_info['username']}")
    lines.append(f"🕒 最后使用: {login_info['last_used']}")
    lines.append(f"🍪 Cookie长度: {len(login_info.get('cookie_string', ''))} 字符")

    return "\n".join(lines)

if __name__ == "__main__":
    # 测试功能
    print("🧪 测试简化账号管理器")
    
    # 测试保存
    print("\n1. 测试保存登录信息:")
    result = save_login_info("<EMAIL>", "test_cookie_123", "测试用户")
    print(f"保存结果: {'✅ 成功' if result else '❌ 失败'}")
    
    # 测试读取
    print("\n2. 测试读取登录信息:")
    info = get_current_login_info()
    if info:
        print("✅ 读取成功:")
        print(f"  邮箱: {info['email']}")
        print(f"  用户名: {info['username']}")
        print(f"  最后使用: {info['last_used']}")
    else:
        print("❌ 读取失败")
    
    # 测试格式化
    print("\n3. 测试格式化状态:")
    status = format_account_status()
    print(status)
    
    # 测试清除
    print("\n4. 测试清除登录信息:")
    clear_result = clear_login_info()
    print(f"清除结果: {'✅ 成功' if clear_result else '❌ 失败'}")
    
    print("\n🎉 测试完成")
