#!/usr/bin/env python3
"""
系统检测视图
显示系统中Augment相关的检测结果
"""

import os
import threading

import customtkinter as ctk
from utils.account_detector import get_all_account_info


class DetectionView:
    """系统检测视图类"""

    def __init__(self, parent_frame):
        """初始化系统检测视图

        Args:
            parent_frame: 父容器框架
        """
        self.parent_frame = parent_frame

        # 创建主框架
        self.main_frame = ctk.CTkFrame(parent_frame, fg_color="transparent")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 页面标题
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="系统检测",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # 创建检测界面
        self._create_detection_interface()

        # 开始检测
        self._start_detection()

    def _create_detection_interface(self):
        """创建检测界面"""
        # 检测状态区域
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🔍 正在检测系统配置...",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.status_label.pack(pady=15)

        # 检测结果区域
        self.results_frame = ctk.CTkFrame(self.main_frame)
        self.results_frame.pack(fill="both", expand=True)

        # 结果标题
        results_title = ctk.CTkLabel(
            self.results_frame,
            text="检测结果",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        results_title.pack(pady=(15, 10))

        # 滚动区域
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.results_frame,
            height=400
        )
        self.scrollable_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # 加载提示
        loading_label = ctk.CTkLabel(
            self.scrollable_frame,
            text="正在检测，请稍候...",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        loading_label.pack(pady=50)

    def _start_detection(self):
        """开始检测"""
        threading.Thread(target=self._detection_worker, daemon=True).start()

    def _detection_worker(self):
        """检测工作线程"""
        try:
            # 执行检测
            detection_results = self._perform_detection()

            # 在主线程中更新UI
            self.parent_frame.after(0, lambda: self._update_results(detection_results))

        except Exception as e:
            # 在主线程中显示错误
            self.parent_frame.after(0, lambda: self._show_error(str(e)))

    def _perform_detection(self):
        """执行系统检测"""
        results = {}

        # 1. 检测AugmentCode账号信息
        try:
            account_info = get_all_account_info()
            results['account'] = {
                'status': 'success',
                'data': account_info
            }
        except Exception as e:
            results['account'] = {
                'status': 'error',
                'error': str(e)
            }

        # 2. 检测VS Code配置
        results['vscode'] = self._detect_vscode()

        # 3. 检测扩展目录
        results['extensions'] = self._detect_extensions()

        # 4. 检测配置文件
        results['config'] = self._detect_config_files()

        # 5. 检测网络连接
        results['network'] = self._detect_network()

        return results

    def _detect_vscode(self):
        """检测VS Code配置"""
        try:
            vscode_paths = [
                os.path.expanduser("~/.vscode"),
                os.path.expanduser("~/AppData/Roaming/Code"),
                os.path.expanduser("~/Library/Application Support/Code")
            ]

            detected_paths = []
            for path in vscode_paths:
                if os.path.exists(path):
                    detected_paths.append(path)

            return {
                'status': 'success' if detected_paths else 'warning',
                'paths': detected_paths,
                'message': f"检测到 {len(detected_paths)} 个VS Code配置目录" if detected_paths else "未检测到VS Code配置"
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

    def _detect_extensions(self):
        """检测扩展目录"""
        try:
            extension_paths = [
                os.path.expanduser("~/.vscode/extensions"),
                os.path.expanduser("~/AppData/Roaming/Code/User/extensions"),
                os.path.expanduser("~/Library/Application Support/Code/User/extensions")
            ]

            augment_extensions = []
            for ext_path in extension_paths:
                if os.path.exists(ext_path):
                    for item in os.listdir(ext_path):
                        if 'augment' in item.lower():
                            augment_extensions.append(os.path.join(ext_path, item))

            return {
                'status': 'success' if augment_extensions else 'info',
                'extensions': augment_extensions,
                'message': f"检测到 {len(augment_extensions)} 个AugmentCode相关扩展" if augment_extensions else "未检测到AugmentCode扩展"
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

    def _detect_config_files(self):
        """检测配置文件"""
        try:
            config_files = []

            # 检测常见配置文件位置
            config_paths = [
                os.path.expanduser("~/.augmentcode"),
                os.path.expanduser("~/AppData/Roaming/augmentcode"),
                os.path.expanduser("~/Library/Application Support/augmentcode")
            ]

            for config_path in config_paths:
                if os.path.exists(config_path):
                    for root, _, files in os.walk(config_path):
                        for file in files:
                            if file.endswith(('.json', '.config', '.cfg')):
                                config_files.append(os.path.join(root, file))

            return {
                'status': 'success' if config_files else 'info',
                'files': config_files,
                'message': f"检测到 {len(config_files)} 个配置文件" if config_files else "未检测到配置文件"
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

    def _detect_network(self):
        """检测网络连接"""
        try:
            # 简单的网络检测
            import socket

            def check_connection(host, port, timeout=3):
                try:
                    socket.create_connection((host, port), timeout)
                    return True
                except (socket.timeout, socket.error):
                    return False

            # 检测常见的服务
            connections = {
                'Google DNS': check_connection('*******', 53),
                'HTTPS': check_connection('www.google.com', 443),
                'AugmentCode API': check_connection('api.augmentcode.com', 443) if True else False  # 假设的API地址
            }

            successful_connections = sum(connections.values())

            return {
                'status': 'success' if successful_connections > 0 else 'warning',
                'connections': connections,
                'message': f"网络连接正常 ({successful_connections}/{len(connections)})" if successful_connections > 0 else "网络连接异常"
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

    def _update_results(self, results):
        """更新检测结果"""
        try:
            # 检查组件是否仍然存在
            if not hasattr(self, 'status_label') or not self.status_label.winfo_exists():
                return

            # 更新状态
            self.status_label.configure(text="✅ 检测完成")

            # 检查滚动框架是否仍然存在
            if not hasattr(self, 'scrollable_frame') or not self.scrollable_frame.winfo_exists():
                return

            # 清空加载提示
            for widget in self.scrollable_frame.winfo_children():
                try:
                    widget.destroy()
                except:
                    pass

            # 显示检测结果
            for category, result in results.items():
                try:
                    self._create_result_section(category, result)
                except:
                    pass
        except Exception as e:
            # 如果更新失败，静默处理
            print(f"检测结果更新失败: {e}")

    def _create_result_section(self, category, result):
        """创建结果区域

        Args:
            category: 检测类别
            result: 检测结果
        """
        # 区域标题
        section_frame = ctk.CTkFrame(self.scrollable_frame)
        section_frame.pack(fill="x", pady=(10, 5))

        # 状态图标和颜色
        status = result.get('status', 'info')
        if status == 'success':
            icon = "✅"
            color = "#4CAF50"
        elif status == 'warning':
            icon = "⚠️"
            color = "#FF9800"
        elif status == 'error':
            icon = "❌"
            color = "#F44336"
        else:
            icon = "ℹ️"
            color = "#2196F3"

        # 标题栏
        title_frame = ctk.CTkFrame(section_frame, fg_color=color, height=36)
        title_frame.pack(fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"{icon} {self._get_category_name(category)}",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="white"
        )
        title_label.pack(pady=8)

        # 内容区域
        content_frame = ctk.CTkFrame(section_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=15)

        # 显示结果信息
        if status == 'error':
            error_label = ctk.CTkLabel(
                content_frame,
                text=f"错误: {result.get('error', '未知错误')}",
                font=ctk.CTkFont(size=12),
                text_color="#F44336"
            )
            error_label.pack(anchor="w", pady=2)
        else:
            # 显示消息
            if 'message' in result:
                message_label = ctk.CTkLabel(
                    content_frame,
                    text=result['message'],
                    font=ctk.CTkFont(size=12, weight="bold")
                )
                message_label.pack(anchor="w", pady=2)

            # 显示详细信息
            self._show_detailed_info(content_frame, category, result)

    def _show_detailed_info(self, parent, category, result):
        """显示详细信息

        Args:
            parent: 父容器
            category: 类别
            result: 结果数据
        """
        if category == 'account':
            self._show_account_details(parent, result.get('data', {}))
        elif category == 'vscode':
            self._show_vscode_details(parent, result)
        elif category == 'extensions':
            self._show_extensions_details(parent, result)
        elif category == 'config':
            self._show_config_details(parent, result)
        elif category == 'network':
            self._show_network_details(parent, result)

    def _show_account_details(self, parent, data):
        """显示账号详细信息"""
        account_data = data.get('augment_account', {})
        if account_data.get('logged_in'):
            details = [
                ("邮箱", account_data.get('email', '未知')),
                ("用户名", account_data.get('username', '未知')),
                ("登录方式", account_data.get('login_method', '未知')),
                ("配置路径", account_data.get('config_path', '未知'))
            ]

            for label, value in details:
                self._create_detail_row(parent, label, value)

    def _show_vscode_details(self, parent, result):
        """显示VS Code详细信息"""
        paths = result.get('paths', [])
        for i, path in enumerate(paths):
            self._create_detail_row(parent, f"路径 {i+1}", path)

    def _show_extensions_details(self, parent, result):
        """显示扩展详细信息"""
        extensions = result.get('extensions', [])
        for i, ext in enumerate(extensions):
            self._create_detail_row(parent, f"扩展 {i+1}", os.path.basename(ext))

    def _show_config_details(self, parent, result):
        """显示配置文件详细信息"""
        files = result.get('files', [])
        for i, file in enumerate(files[:5]):  # 只显示前5个
            self._create_detail_row(parent, f"文件 {i+1}", os.path.basename(file))

        if len(files) > 5:
            more_label = ctk.CTkLabel(
                parent,
                text=f"... 还有 {len(files) - 5} 个文件",
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            more_label.pack(anchor="w", pady=2)

    def _show_network_details(self, parent, result):
        """显示网络详细信息"""
        connections = result.get('connections', {})
        for service, status in connections.items():
            status_text = "✅ 连接正常" if status else "❌ 连接失败"
            self._create_detail_row(parent, service, status_text)

    def _create_detail_row(self, parent, label, value):
        """创建详细信息行"""
        row_frame = ctk.CTkFrame(parent, fg_color="transparent")
        row_frame.pack(fill="x", pady=1)

        label_widget = ctk.CTkLabel(
            row_frame,
            text=f"{label}:",
            font=ctk.CTkFont(size=11, weight="bold"),
            width=100
        )
        label_widget.pack(side="left", padx=5)

        value_widget = ctk.CTkLabel(
            row_frame,
            text=str(value),
            font=ctk.CTkFont(size=11),
            anchor="w"
        )
        value_widget.pack(side="left", fill="x", expand=True, padx=5)

    def _get_category_name(self, category):
        """获取类别名称"""
        names = {
            'account': 'AugmentCode账号',
            'vscode': 'VS Code配置',
            'extensions': '扩展检测',
            'config': '配置文件',
            'network': '网络连接'
        }
        return names.get(category, category)

    def _show_error(self, error_message):
        """显示错误信息"""
        try:
            # 检查组件是否仍然存在
            if not hasattr(self, 'status_label') or not self.status_label.winfo_exists():
                return

            self.status_label.configure(text="❌ 检测失败")

            # 检查滚动框架是否仍然存在
            if not hasattr(self, 'scrollable_frame') or not self.scrollable_frame.winfo_exists():
                return

            # 清空加载提示
            for widget in self.scrollable_frame.winfo_children():
                try:
                    widget.destroy()
                except:
                    pass

            # 显示错误信息
            error_frame = ctk.CTkFrame(self.scrollable_frame)
            error_frame.pack(fill="x", pady=20)

            error_label = ctk.CTkLabel(
                error_frame,
                text=f"检测过程中发生错误：{error_message}",
                font=ctk.CTkFont(size=14),
                text_color="#F44336"
            )
            error_label.pack(pady=20)
        except Exception as e:
            # 如果显示错误失败，静默处理
            print(f"错误显示失败: {e}")