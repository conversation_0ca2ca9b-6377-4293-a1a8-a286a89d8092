#!/usr/bin/env python3
"""
Cookie获取助手
帮助用户获取AugmentCode的Cookie
"""

import sys
import os
import webbrowser
import time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

class CookieHelper:
    """Cookie获取助手"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AugmentCode Cookie获取助手")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🍪 AugmentCode Cookie获取助手", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 说明文本
        info_text = """
📋 获取Cookie的步骤：

1️⃣ 点击下方按钮打开AugmentCode网站
2️⃣ 登录您的账号
3️⃣ 按F12打开开发者工具
4️⃣ 切换到"Network"（网络）标签页
5️⃣ 刷新页面或访问订阅页面
6️⃣ 找到任意请求，查看Request Headers
7️⃣ 复制Cookie字段的完整内容
8️⃣ 粘贴到下方文本框中
9️⃣ 点击验证和保存

💡 提示：Cookie通常很长，确保复制完整！
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
        info_label.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        button_frame.columnconfigure(2, weight=1)
        
        # 按钮
        self.open_website_btn = ttk.Button(button_frame, text="🌐 打开AugmentCode网站", 
                                          command=self.open_website)
        self.open_website_btn.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        
        self.open_subscription_btn = ttk.Button(button_frame, text="📊 打开订阅页面", 
                                               command=self.open_subscription_page)
        self.open_subscription_btn.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        
        self.show_guide_btn = ttk.Button(button_frame, text="📖 详细教程", 
                                        command=self.show_detailed_guide)
        self.show_guide_btn.grid(row=0, column=2, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # Cookie输入区域
        cookie_frame = ttk.LabelFrame(main_frame, text="🍪 Cookie输入区域", padding="10")
        cookie_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        cookie_frame.columnconfigure(0, weight=1)
        cookie_frame.rowconfigure(0, weight=1)
        
        # Cookie文本框
        self.cookie_text = scrolledtext.ScrolledText(cookie_frame, height=8, wrap=tk.WORD)
        self.cookie_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Cookie操作按钮
        cookie_button_frame = ttk.Frame(cookie_frame)
        cookie_button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        cookie_button_frame.columnconfigure(0, weight=1)
        cookie_button_frame.columnconfigure(1, weight=1)
        cookie_button_frame.columnconfigure(2, weight=1)
        
        self.paste_btn = ttk.Button(cookie_button_frame, text="📋 粘贴Cookie", 
                                   command=self.paste_cookie)
        self.paste_btn.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        
        self.validate_btn = ttk.Button(cookie_button_frame, text="✅ 验证Cookie", 
                                      command=self.validate_cookie)
        self.validate_btn.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        
        self.save_btn = ttk.Button(cookie_button_frame, text="💾 保存Cookie", 
                                  command=self.save_cookie)
        self.save_btn.grid(row=0, column=2, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # 状态显示
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                foreground="blue")
        status_label.grid(row=4, column=0, pady=(10, 0))
        
        # 添加示例Cookie格式
        example_text = """
💡 Cookie格式示例：
session=abc123; auth_token=xyz789; user_id=12345; _ga=GA1.2.123456789; ...

⚠️ 注意：
• Cookie通常包含多个键值对，用分号分隔
• 确保复制完整的Cookie字符串
• 不要包含"Cookie: "前缀
        """
        
        example_label = ttk.Label(main_frame, text=example_text, justify=tk.LEFT, 
                                 foreground="gray")
        example_label.grid(row=5, column=0, pady=(10, 0))
    
    def open_website(self):
        """打开AugmentCode网站"""
        try:
            webbrowser.open("https://app.augmentcode.com")
            self.status_var.set("✅ 已打开AugmentCode网站")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开网站: {e}")
    
    def open_subscription_page(self):
        """打开订阅页面"""
        try:
            webbrowser.open("https://app.augmentcode.com/account/subscription")
            self.status_var.set("✅ 已打开订阅页面")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开订阅页面: {e}")
    
    def show_detailed_guide(self):
        """显示详细教程"""
        guide_window = tk.Toplevel(self.root)
        guide_window.title("Cookie获取详细教程")
        guide_window.geometry("700x500")
        guide_window.resizable(True, True)
        
        # 创建滚动文本框
        text_frame = ttk.Frame(guide_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        guide_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD)
        guide_text.pack(fill=tk.BOTH, expand=True)
        
        detailed_guide = """
🍪 AugmentCode Cookie获取详细教程

📋 方法一：使用Chrome浏览器

1️⃣ 打开Chrome浏览器
2️⃣ 访问 https://app.augmentcode.com
3️⃣ 登录您的AugmentCode账号
4️⃣ 登录成功后，按F12打开开发者工具
5️⃣ 点击"Network"（网络）标签页
6️⃣ 刷新页面（Ctrl+R 或 F5）
7️⃣ 在网络请求列表中找到任意请求（通常是第一个）
8️⃣ 点击该请求，查看右侧的详细信息
9️⃣ 找到"Request Headers"部分
🔟 找到"Cookie:"行，复制其后的完整内容

📋 方法二：使用Application标签页

1️⃣ 在开发者工具中点击"Application"标签页
2️⃣ 在左侧找到"Storage" > "Cookies"
3️⃣ 点击"https://app.augmentcode.com"
4️⃣ 右侧会显示所有Cookie
5️⃣ 手动复制所有Cookie，格式为：name1=value1; name2=value2; ...

📋 方法三：使用Console控制台

1️⃣ 在开发者工具中点击"Console"标签页
2️⃣ 输入以下代码并按回车：
   document.cookie
3️⃣ 复制输出的完整Cookie字符串

⚠️ 重要提示：

• Cookie包含敏感信息，请妥善保管
• 不要在公共场所或不安全的网络环境下操作
• Cookie有有效期，过期后需要重新获取
• 确保复制的Cookie完整且正确

🔧 常见问题：

Q: Cookie太长，复制不完整怎么办？
A: 可以分段复制，或者使用Ctrl+A全选后复制

Q: 获取的Cookie无效怎么办？
A: 确保在登录状态下获取，并检查Cookie格式

Q: 多久需要重新获取Cookie？
A: 通常Cookie会在一段时间后过期，建议定期更新

💡 验证Cookie是否正确：

获取Cookie后，可以使用本工具的验证功能检查Cookie是否有效。
有效的Cookie应该能够访问您的账号信息和订阅详情。
        """
        
        guide_text.insert(tk.END, detailed_guide)
        guide_text.config(state=tk.DISABLED)
    
    def paste_cookie(self):
        """粘贴Cookie"""
        try:
            # 获取剪贴板内容
            clipboard_content = self.root.clipboard_get()
            
            # 清空文本框并插入内容
            self.cookie_text.delete(1.0, tk.END)
            self.cookie_text.insert(1.0, clipboard_content)
            
            self.status_var.set("✅ 已粘贴Cookie内容")
        except tk.TclError:
            messagebox.showwarning("警告", "剪贴板为空或无法访问")
        except Exception as e:
            messagebox.showerror("错误", f"粘贴失败: {e}")
    
    def validate_cookie(self):
        """验证Cookie"""
        cookie_content = self.cookie_text.get(1.0, tk.END).strip()
        
        if not cookie_content:
            messagebox.showwarning("警告", "请先输入Cookie内容")
            return
        
        self.status_var.set("🔄 正在验证Cookie...")
        
        # 在新线程中验证Cookie
        def validate_thread():
            try:
                from cookie_validator_and_refresher import CookieValidator
                
                validator = CookieValidator()
                valid, validation_info = validator.validate_cookie(cookie_content)
                
                # 在主线程中更新UI
                self.root.after(0, self.show_validation_result, valid, validation_info)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"验证失败: {e}"))
                self.root.after(0, lambda: self.status_var.set("❌ 验证失败"))
        
        threading.Thread(target=validate_thread, daemon=True).start()
    
    def show_validation_result(self, valid, validation_info):
        """显示验证结果"""
        if valid:
            self.status_var.set("✅ Cookie验证成功")
            messagebox.showinfo("验证成功", 
                              f"Cookie验证成功！\n\n"
                              f"状态: {validation_info.get('status', '有效')}\n"
                              f"响应码: {validation_info.get('response_code', 200)}")
        else:
            self.status_var.set("❌ Cookie验证失败")
            error_msg = validation_info.get('error', validation_info.get('status', '未知错误'))
            messagebox.showerror("验证失败", 
                               f"Cookie验证失败！\n\n"
                               f"错误: {error_msg}\n\n"
                               f"建议:\n"
                               f"• 确保已登录AugmentCode\n"
                               f"• 检查Cookie格式是否正确\n"
                               f"• 尝试重新获取Cookie")
    
    def save_cookie(self):
        """保存Cookie"""
        cookie_content = self.cookie_text.get(1.0, tk.END).strip()
        
        if not cookie_content:
            messagebox.showwarning("警告", "请先输入Cookie内容")
            return
        
        # 询问用户邮箱
        email = tk.simpledialog.askstring("输入邮箱", 
                                         "请输入您的AugmentCode邮箱地址:",
                                         parent=self.root)
        
        if not email:
            return
        
        try:
            from utils.account_manager import save_account_login
            
            # 保存账号信息
            success = save_account_login(email, cookie_content, email.split('@')[0])
            
            if success:
                self.status_var.set("✅ Cookie保存成功")
                messagebox.showinfo("保存成功", 
                                  f"Cookie已成功保存！\n\n"
                                  f"账号: {email}\n"
                                  f"Cookie长度: {len(cookie_content)} 字符\n\n"
                                  f"现在可以关闭此窗口，使用主程序查看账号信息。")
            else:
                messagebox.showerror("保存失败", "Cookie保存失败，请检查输入信息")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存Cookie时出错: {e}")
    
    def run(self):
        """运行助手"""
        self.root.mainloop()


def main():
    """主函数"""
    print("🍪 启动Cookie获取助手...")
    
    try:
        # 导入tkinter.simpledialog
        import tkinter.simpledialog
        
        helper = CookieHelper()
        helper.run()
        
    except ImportError as e:
        print(f"❌ 缺少必要的库: {e}")
        print("💡 请确保已安装tkinter")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
